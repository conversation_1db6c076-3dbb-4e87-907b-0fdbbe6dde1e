package testing

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

func saveCookieToFile(token string, userid int64) error {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %v", err)
	}
	fmt.Println("homeDir:", homeDir)
	// 创建配置目录路径
	configDir := filepath.Join(homeDir, ".config", "gomusic")

	// 确保目录存在
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}

	// 创建cookie文件路径
	cookieFile := filepath.Join(configDir, "cookies1.txt")

	// 构建cookie内容
	cookieContent := fmt.Sprintf("token=%s;userid=%d", token, userid)
	fmt.Println("cookieContent:", cookieFile)
	fmt.Println("cookieContent:", cookieContent)

	// 写入文件
	if err := os.WriteFile(cookieFile, []byte(cookieContent), 0644); err != nil {
		return fmt.Errorf("写入cookie文件失败: %v", err)
	}

	return nil
}

// token=cd43ccbc9efca07c6f1bacc247bfa867513f8bd054b05861852bdec9a12eccdf;userid=1191120886
func TestSave(t *testing.T) {
	err := saveCookieToFile("cd43ccbc9efca07c6f1bacc247bfa8679ee250636ab1815ce30287d35d7c92cc", 1191120886)
	if err != nil {
		t.Errorf("保存cookie失败: %v", err)
	}
}
