/* 磨砂黑主题样式 - 深色磨砂玻璃效果 */
:root[data-theme="frosted-dark"] {
    /* 主调色：深色磨砂玻璃效果的半透明色系 */
    --primary-base: rgba(0, 0, 0, 0.1);
    --primary-color: rgba(0, 0, 0, 0.15);
    --primary-light: rgba(0, 0, 0, 0.2);
    --primary-dark: rgba(0, 0, 0, 0.05);

    /* 背景色系 - 深色磨砂玻璃效果 */
    --bg-color: rgba(15, 15, 20, 0.9);
    --bg-secondary: rgba(20, 20, 25, 0.95);
    --bg-elevated: rgba(0, 0, 0, 0.3);
    --bg-tertiary: rgba(10, 10, 15, 0.8);
    --hover-bg: rgba(255, 255, 255, 0.1);

    /* 文字色系 - 适配深色磨砂背景 */
    --text-primary: rgba(240, 240, 245, 0.95);
    --text-secondary: rgba(200, 200, 210, 0.85);
    --text-tertiary: rgba(160, 160, 170, 0.75);
    --text-inverse: rgba(20, 20, 25, 0.95);

    /* 边框和分割线 - 深色磨砂效果，模糊边框 */
    --border-color: rgba(255, 255, 255, 0.08);
    --border-light: rgba(255, 255, 255, 0.04);

    /* 阴影 - 深色磨砂玻璃阴影效果 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 16px 64px rgba(0, 0, 0, 0.5);

    /* 强调色 - 磨砂黑主题专用 */
    --accent-color: rgba(139, 92, 246, 0.9);
    --accent-color-rgb: 139, 92, 246;
    --accent-hover: rgba(124, 58, 237, 1);

    /* 组件特定颜色 */
    --titlebar-bg: rgba(0, 0, 0, 0.2);
    --card-bg: rgba(0, 0, 0, 0.25);
    --input-bg: rgba(0, 0, 0, 0.3);
    --input-border: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(0, 0, 0, 0.4);
    --player-bg: rgba(0, 0, 0, 0.4);
    --player-control-bg: rgba(0, 0, 0, 0.3);
    --player-progress-bg: rgba(255, 255, 255, 0.2);
    --player-progress-fill: var(--accent-color);

    /* 磨砂玻璃特效变量 */
    --blur-strength: 25px;
    --backdrop-opacity: 0.9;
}

/* 磨砂黑主题全局背景 */
[data-theme="frosted-dark"] body {
    background: linear-gradient(135deg, 
        rgba(10, 10, 15, 0.95) 0%, 
        rgba(20, 15, 25, 0.95) 25%,
        rgba(15, 20, 25, 0.95) 50%,
        rgba(25, 15, 20, 0.95) 75%,
        rgba(15, 25, 20, 0.95) 100%);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
}

/* 磨砂黑主题标题栏 */
[data-theme="frosted-dark"] .custom-titlebar {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 磨砂黑主题按钮样式 - 模糊边框 */
[data-theme="frosted-dark"] .titlebar-btn {
    background: rgba(0, 0, 0, 0.2);
    color: var(--text-secondary);
    border: 1px solid transparent;
    border-radius: 8px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 0 0 1px rgba(255, 255, 255, 0.03);
}

[data-theme="frosted-dark"] .titlebar-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 磨砂黑主题标题文字 */
[data-theme="frosted-dark"] .titlebar-title {
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 磨砂黑主题侧边栏 - 模糊边框 */
[data-theme="frosted-dark"] .sidebar {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border-right: 1px solid transparent;
    box-shadow: 1px 0 0 0 rgba(255, 255, 255, 0.05);
}

/* 磨砂黑主题侧边栏文字和图标 */
[data-theme="frosted-dark"] .list-item {
    color: rgba(240, 240, 245, 0.9);
}

[data-theme="frosted-dark"] .list-item i {
    color: rgba(240, 240, 245, 0.85);
}

[data-theme="frosted-dark"] .item-text {
    color: rgba(245, 245, 250, 0.95);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="frosted-dark"] .list-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(250, 250, 255, 1);
}

[data-theme="frosted-dark"] .list-item:hover i {
    color: rgba(250, 250, 255, 0.95);
}

[data-theme="frosted-dark"] .list-item:hover .item-text {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

[data-theme="frosted-dark"] .list-item.active {
    background: rgba(139, 92, 246, 0.2);
    color: rgba(139, 92, 246, 1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

[data-theme="frosted-dark"] .list-item.active i {
    color: rgba(139, 92, 246, 1);
}

/* 磨砂黑主题侧边栏头部 */
[data-theme="frosted-dark"] .sidebar-header {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 磨砂黑主题"我的"文字样式 */
[data-theme="frosted-dark"] .my-music-text {
    color: rgba(245, 245, 250, 0.95);
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* 磨砂黑主题卡片 - 模糊边框 */
[data-theme="frosted-dark"] .card,
[data-theme="frosted-dark"] .song-item,
[data-theme="frosted-dark"] .playlist-card,
[data-theme="frosted-dark"] .artist-card {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid transparent;
    border-radius: 12px;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.04),
                inset 0 0 0 1px rgba(255, 255, 255, 0.02),
                0 4px 16px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
}

[data-theme="frosted-dark"] .card:hover,
[data-theme="frosted-dark"] .song-item:hover,
[data-theme="frosted-dark"] .playlist-card:hover,
[data-theme="frosted-dark"] .artist-card:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08),
                inset 0 0 0 1px rgba(255, 255, 255, 0.04),
                0 8px 24px rgba(0, 0, 0, 0.5);
}

/* 磨砂黑主题播放器 */
[data-theme="frosted-dark"] .player-container {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 磨砂黑主题播放器底栏 */
[data-theme="frosted-dark"] .player-bar {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
}

[data-theme="frosted-dark"] .player-controls button,
[data-theme="frosted-dark"] .player-control-btn {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid transparent;
    border-radius: 50%;
    transition: all 0.3s ease;
    color: var(--text-primary);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.06),
                inset 0 0 0 1px rgba(255, 255, 255, 0.03);
}

[data-theme="frosted-dark"] .player-controls button:hover,
[data-theme="frosted-dark"] .player-control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.12),
                inset 0 0 0 1px rgba(255, 255, 255, 0.06),
                0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 磨砂黑主题输入框 - 模糊边框 */
[data-theme="frosted-dark"] input,
[data-theme="frosted-dark"] select,
[data-theme="frosted-dark"] textarea {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid transparent;
    border-radius: 8px;
    color: var(--text-primary);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.06),
                inset 0 0 0 1px rgba(255, 255, 255, 0.03);
}

[data-theme="frosted-dark"] input:focus,
[data-theme="frosted-dark"] select:focus,
[data-theme="frosted-dark"] textarea:focus {
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 0 1px var(--accent-color),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                0 0 0 3px rgba(139, 92, 246, 0.2);
}

/* 磨砂黑主题滚动条 */
[data-theme="frosted-dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="frosted-dark"] ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

[data-theme="frosted-dark"] ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

[data-theme="frosted-dark"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 磨砂黑主题模态框 - 模糊边框 */
[data-theme="frosted-dark"] .modal,
[data-theme="frosted-dark"] .dialog,
[data-theme="frosted-dark"] .modal-content,
[data-theme="frosted-dark"] .close-confirm-content {
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(35px);
    -webkit-backdrop-filter: blur(35px);
    border: 1px solid transparent;
    border-radius: 16px;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08),
                inset 0 0 0 1px rgba(255, 255, 255, 0.04),
                0 20px 60px rgba(0, 0, 0, 0.6);
}

/* 磨砂黑主题模态框遮罩层 */
[data-theme="frosted-dark"] .modal-overlay {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 磨砂黑主题模态框标题 */
[data-theme="frosted-dark"] .modal-title {
    color: var(--text-primary);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 磨砂黑主题表单输入框 - 模糊边框 */
[data-theme="frosted-dark"] .form-input {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid transparent;
    color: var(--text-primary);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08),
                inset 0 0 0 1px rgba(255, 255, 255, 0.04);
}

[data-theme="frosted-dark"] .form-input:focus {
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.8),
                inset 0 0 0 1px rgba(255, 255, 255, 0.06),
                0 0 0 3px rgba(139, 92, 246, 0.2);
}

[data-theme="frosted-dark"] .form-input::placeholder {
    color: var(--text-tertiary);
}

/* 磨砂黑主题按钮 - 模糊边框 */
[data-theme="frosted-dark"] .login-btn,
[data-theme="frosted-dark"] .send-code-btn {
    background: rgba(139, 92, 246, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid transparent;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.3),
                inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

[data-theme="frosted-dark"] .login-btn:hover,
[data-theme="frosted-dark"] .send-code-btn:hover {
    background: rgba(124, 58, 237, 1);
    transform: translateY(-2px);
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.5),
                inset 0 0 0 1px rgba(255, 255, 255, 0.15),
                0 8px 25px rgba(139, 92, 246, 0.4);
}

/* 磨砂黑主题进度条 */
[data-theme="frosted-dark"] .progress-bar {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

[data-theme="frosted-dark"] .progress-fill {
    background: linear-gradient(90deg,
        rgba(139, 92, 246, 0.9) 0%,
        rgba(124, 58, 237, 1) 100%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 磨砂黑主题沉浸式播放器 */
[data-theme="frosted-dark"] .immersive-player {
    background: linear-gradient(135deg,
        rgba(10, 10, 15, 0.98) 0%,
        rgba(20, 15, 25, 0.98) 25%,
        rgba(15, 20, 25, 0.98) 50%,
        rgba(25, 15, 20, 0.98) 75%,
        rgba(15, 25, 20, 0.98) 100%);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
}

[data-theme="frosted-dark"] .immersive-controls button {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

[data-theme="frosted-dark"] .immersive-controls button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 磨砂黑主题特殊效果 */
[data-theme="frosted-dark"] .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

/* 骨架屏动画变量 */
:root[data-theme="frosted-dark"] {
    --skeleton-bg: rgba(0, 0, 0, 0.3);
    --skeleton-shimmer: rgba(255, 255, 255, 0.1);
}

/* 磨砂黑主题骨架屏 */
[data-theme="frosted-dark"] .skeleton {
    background: linear-gradient(90deg,
        var(--skeleton-bg) 25%,
        var(--skeleton-shimmer) 50%,
        var(--skeleton-bg) 75%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 磨砂黑主题搜索框 - 模糊边框 */
[data-theme="frosted-dark"] .search-input {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid transparent;
    color: var(--text-primary);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.06),
                inset 0 0 0 1px rgba(255, 255, 255, 0.03);
}

[data-theme="frosted-dark"] .search-input:focus {
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 0 1px var(--accent-color),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                0 0 0 3px rgba(139, 92, 246, 0.2);
}

[data-theme="frosted-dark"] .search-input::placeholder {
    color: var(--text-tertiary);
}

/* 磨砂黑主题额外的模糊边框样式 */
[data-theme="frosted-dark"] .btn,
[data-theme="frosted-dark"] .button {
    border: 1px solid transparent;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 0 0 1px rgba(255, 255, 255, 0.02);
}

[data-theme="frosted-dark"] .btn:hover,
[data-theme="frosted-dark"] .button:hover {
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* 磨砂黑主题分割线模糊效果 */
[data-theme="frosted-dark"] .divider,
[data-theme="frosted-dark"] .separator,
[data-theme="frosted-dark"] hr {
    border: none;
    height: 1px;
    background: transparent;
    box-shadow: 0 0 0 0.5px rgba(255, 255, 255, 0.04);
}

/* 磨砂黑主题标题栏模糊边框 */
[data-theme="frosted-dark"] .custom-titlebar {
    border-bottom: 1px solid transparent;
    box-shadow: 0 1px 0 0 rgba(255, 255, 255, 0.04);
}

/* 磨砂黑主题播放器底栏模糊边框 */
[data-theme="frosted-dark"] .player-bar {
    border-top: 1px solid transparent;
    box-shadow: 0 -1px 0 0 rgba(255, 255, 255, 0.06);
}

/* 磨砂黑主题对话框按钮 - 模糊边框 */
[data-theme="frosted-dark"] .btn-secondary {
    background: rgba(0, 0, 0, 0.3);
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid transparent;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08),
                inset 0 0 0 1px rgba(255, 255, 255, 0.04);
}

[data-theme="frosted-dark"] .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.15),
                inset 0 0 0 1px rgba(255, 255, 255, 0.08);
}

[data-theme="frosted-dark"] .btn-primary {
    background: rgba(139, 92, 246, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid transparent;
    color: white;
    box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.3),
                inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

[data-theme="frosted-dark"] .btn-primary:hover {
    background: rgba(124, 58, 237, 1);
    box-shadow: 0 0 0 1px rgba(124, 58, 237, 0.5),
                inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

/* 设置页面下拉列表磨砂暗色主题 */
[data-theme="frosted-dark"] .settings-select {
    background: rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    border: 1px solid transparent !important;
    color: rgba(255, 255, 255, 0.9) !important;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.06),
                inset 0 0 0 1px rgba(255, 255, 255, 0.03) !important;
    /* 磨砂主题下拉箭头 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

[data-theme="frosted-dark"] .settings-select:focus {
    background: rgba(0, 0, 0, 0.4) !important;
    box-shadow: 0 0 0 1px var(--accent-color),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05),
                0 0 0 3px rgba(139, 92, 246, 0.2) !important;
    outline: none;
}

[data-theme="frosted-dark"] .settings-select:hover {
    background: rgba(0, 0, 0, 0.35) !important;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* 磨砂暗色主题下拉选项 */
[data-theme="frosted-dark"] .settings-select option {
    background: rgba(0, 0, 0, 0.8) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 8px 12px;
}

[data-theme="frosted-dark"] .settings-select option:checked {
    background: rgba(139, 92, 246, 0.8) !important;
    color: white !important;
}

[data-theme="frosted-dark"] .settings-select option:hover {
    background: rgba(0, 0, 0, 0.6) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}
