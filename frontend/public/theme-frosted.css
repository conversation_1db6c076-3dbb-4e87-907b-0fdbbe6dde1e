/* 磨砂主题样式 - 基于磨砂玻璃效果 */
:root[data-theme="frosted"] {
    /* 主调色：磨砂玻璃效果的半透明色系 */
    --primary-base: rgba(255, 255, 255, 0.1);
    --primary-color: rgba(255, 255, 255, 0.15);
    --primary-light: rgba(255, 255, 255, 0.2);
    --primary-dark: rgba(255, 255, 255, 0.05);

    /* 背景色系 - 磨砂玻璃效果 */
    --bg-color: rgba(240, 240, 245, 0.8);
    --bg-secondary: rgba(235, 235, 240, 0.9);
    --bg-elevated: rgba(255, 255, 255, 0.25);
    --bg-tertiary: rgba(250, 250, 255, 0.3);
    --hover-bg: rgba(255, 255, 255, 0.15);

    /* 文字色系 - 适配磨砂背景，增强对比度 */
    --text-primary: rgba(20, 20, 25, 0.95);
    --text-secondary: rgba(40, 40, 50, 0.85);
    --text-tertiary: rgba(80, 80, 90, 0.75);
    --text-inverse: rgba(255, 255, 255, 0.95);

    /* 边框和分割线 - 磨砂效果 */
    --border-color: rgba(255, 255, 255, 0.2);
    --border-light: rgba(255, 255, 255, 0.1);

    /* 阴影 - 磨砂玻璃阴影效果 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 16px 64px rgba(0, 0, 0, 0.2);

    /* 强调色 - 磨砂主题专用 */
    --accent-color: rgba(99, 102, 241, 0.8);
    --accent-color-rgb: 99, 102, 241;
    --accent-hover: rgba(79, 70, 229, 0.9);

    /* 组件特定颜色 */
    --titlebar-bg: rgba(255, 255, 255, 0.15);
    --card-bg: rgba(255, 255, 255, 0.2);
    --input-bg: rgba(255, 255, 255, 0.25);
    --input-border: rgba(255, 255, 255, 0.3);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --player-bg: rgba(255, 255, 255, 0.45);
    --player-control-bg: rgba(255, 255, 255, 0.3);
    --player-progress-bg: rgba(255, 255, 255, 0.3);
    --player-progress-fill: var(--accent-color);

    /* 磨砂玻璃特效变量 */
    --blur-strength: 20px;
    --backdrop-opacity: 0.8;
}

/* 磨砂主题全局背景 */
[data-theme="frosted"] body {
    background: linear-gradient(135deg, 
        rgba(240, 240, 255, 0.9) 0%, 
        rgba(255, 240, 245, 0.9) 25%,
        rgba(245, 255, 240, 0.9) 50%,
        rgba(240, 245, 255, 0.9) 75%,
        rgba(255, 245, 240, 0.9) 100%);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
}

/* 磨砂主题标题栏 */
[data-theme="frosted"] .custom-titlebar {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* 磨砂主题按钮样式 */
[data-theme="frosted"] .titlebar-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

[data-theme="frosted"] .titlebar-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 磨砂主题标题文字 */
[data-theme="frosted"] .titlebar-title {
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* 磨砂主题侧边栏 */
[data-theme="frosted"] .sidebar {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border-right: 1px solid rgba(255, 255, 255, 0.3);
}

/* 磨砂主题侧边栏文字和图标 - 增强对比度 */
[data-theme="frosted"] .list-item {
    color: rgba(20, 20, 25, 0.9);
}

[data-theme="frosted"] .list-item i {
    color: rgba(20, 20, 25, 0.85);
}

[data-theme="frosted"] .item-text {
    color: rgba(15, 15, 20, 0.95);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.2);
}

[data-theme="frosted"] .list-item:hover {
    background: rgba(255, 255, 255, 0.35);
    color: rgba(10, 10, 15, 1);
}

[data-theme="frosted"] .list-item:hover i {
    color: rgba(10, 10, 15, 0.95);
}

[data-theme="frosted"] .list-item:hover .item-text {
    color: rgba(10, 10, 15, 1);
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.3);
}

[data-theme="frosted"] .list-item.active {
    background: rgba(99, 102, 241, 0.25);
    color: rgba(99, 102, 241, 1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

[data-theme="frosted"] .list-item.active i {
    color: rgba(99, 102, 241, 1);
}

/* 磨砂主题侧边栏头部 */
[data-theme="frosted"] .sidebar-header {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.25);
}

/* 磨砂主题侧边栏分割线 */
[data-theme="frosted"] .sidebar-divider {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 磨砂主题侧边栏切换按钮 */
[data-theme="frosted"] .sidebar-toggle-btn {
    background: rgba(255, 255, 255, 0.25);
    color: rgba(20, 20, 25, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

[data-theme="frosted"] .sidebar-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    color: rgba(10, 10, 15, 0.95);
    border-color: rgba(255, 255, 255, 0.45);
    transform: translateX(2px);
}

/* 磨砂主题"我的"文字样式 */
[data-theme="frosted"] .my-music-text {
    color: rgba(15, 15, 20, 0.95);
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.4);
}

/* 磨砂主题卡片 */
[data-theme="frosted"] .card,
[data-theme="frosted"] .song-item,
[data-theme="frosted"] .playlist-card,
[data-theme="frosted"] .artist-card {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

[data-theme="frosted"] .card:hover,
[data-theme="frosted"] .song-item:hover,
[data-theme="frosted"] .playlist-card:hover,
[data-theme="frosted"] .artist-card:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 磨砂主题播放器 */
[data-theme="frosted"] .player-container {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border-top: 1px solid rgba(255, 255, 255, 0.3);
}

/* 磨砂主题播放器底栏 - 增强不透明度 */
[data-theme="frosted"] .player-bar {
    background: rgba(255, 255, 255, 0.45);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border-top: 1px solid rgba(255, 255, 255, 0.35);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

[data-theme="frosted"] .player-controls button,
[data-theme="frosted"] .player-control-btn {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.35);
    border-radius: 50%;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

[data-theme="frosted"] .player-controls button:hover,
[data-theme="frosted"] .player-control-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 磨砂主题播放器进度条 */
[data-theme="frosted"] .progress-bar {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 4px;
}

[data-theme="frosted"] .progress-fill {
    background: linear-gradient(90deg,
        rgba(99, 102, 241, 0.9) 0%,
        rgba(79, 70, 229, 1) 100%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 磨砂主题歌曲信息区域 */
[data-theme="frosted"] .song-info {
    color: var(--text-primary);
}

[data-theme="frosted"] .songname {
    color: var(--text-primary);
    font-weight: 600;
}

[data-theme="frosted"] .artist-name {
    color: var(--text-secondary);
}

/* 磨砂主题音量控制 */
[data-theme="frosted"] .volume-slider {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

[data-theme="frosted"] .volume-slider::-webkit-slider-thumb {
    background: rgba(99, 102, 241, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 磨砂主题时间显示 */
[data-theme="frosted"] .time-current,
[data-theme="frosted"] .time-total {
    color: var(--text-primary);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

/* 磨砂主题歌曲封面 */
[data-theme="frosted"] .song-cover {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 磨砂主题播放器额外控制按钮 */
[data-theme="frosted"] .player-extra-controls button {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

[data-theme="frosted"] .player-extra-controls button:hover {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.4);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* 磨砂主题输入框 */
[data-theme="frosted"] input,
[data-theme="frosted"] select,
[data-theme="frosted"] textarea {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: var(--text-primary);
}

[data-theme="frosted"] input:focus,
[data-theme="frosted"] select:focus,
[data-theme="frosted"] textarea:focus {
    background: rgba(255, 255, 255, 0.35);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* 磨砂主题滚动条 */
[data-theme="frosted"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="frosted"] ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

[data-theme="frosted"] ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

[data-theme="frosted"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 磨砂主题模态框 - 降低透明度增强可读性 */
[data-theme="frosted"] .modal,
[data-theme="frosted"] .dialog,
[data-theme="frosted"] .modal-content,
[data-theme="frosted"] .close-confirm-content {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
}

/* 磨砂主题模态框遮罩层 */
[data-theme="frosted"] .modal-overlay {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* 磨砂主题登录弹窗头部 */
[data-theme="frosted"] .modal-header {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.4);
}

/* 磨砂主题模态框标题 */
[data-theme="frosted"] .modal-title {
    color: var(--text-primary);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* 磨砂主题关闭按钮 */
[data-theme="frosted"] .modal-close-btn {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.25);
}

[data-theme="frosted"] .modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

/* 磨砂主题模态框主体 */
[data-theme="frosted"] .modal-body {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 磨砂主题登录标签页 */
[data-theme="frosted"] .login-tabs .tab-btn {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: 2px solid transparent;
}

[data-theme="frosted"] .login-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="frosted"] .login-tabs .tab-btn.active {
    background: rgba(99, 102, 241, 0.2);
    color: rgba(99, 102, 241, 1);
    border-color: rgba(255, 255, 255, 0.3);
    border-bottom-color: rgba(99, 102, 241, 0.8);
}

/* 磨砂主题表单输入框 */
[data-theme="frosted"] .form-input {
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: var(--text-primary);
}

[data-theme="frosted"] .form-input:focus {
    background: rgba(255, 255, 255, 0.5);
    border-color: rgba(99, 102, 241, 0.8);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

[data-theme="frosted"] .form-input::placeholder {
    color: var(--text-tertiary);
}

/* 磨砂主题按钮 */
[data-theme="frosted"] .login-btn,
[data-theme="frosted"] .send-code-btn {
    background: rgba(99, 102, 241, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(99, 102, 241, 0.5);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

[data-theme="frosted"] .login-btn:hover,
[data-theme="frosted"] .send-code-btn:hover {
    background: rgba(79, 70, 229, 1);
    border-color: rgba(79, 70, 229, 0.7);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

/* 磨砂主题关闭确认对话框 */
[data-theme="frosted"] .close-confirm-modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

[data-theme="frosted"] .close-confirm-message {
    background: rgba(99, 102, 241, 0.15);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(99, 102, 241, 0.25);
}

[data-theme="frosted"] .close-confirm-icon {
    color: rgba(99, 102, 241, 1);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

[data-theme="frosted"] .close-confirm-message p {
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.2);
}

/* 磨砂主题选项按钮 */
[data-theme="frosted"] .close-option input[type="radio"] + label {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: var(--text-primary);
}

[data-theme="frosted"] .close-option input[type="radio"]:checked + label {
    background: rgba(99, 102, 241, 0.25);
    border-color: rgba(99, 102, 241, 0.5);
    color: rgba(99, 102, 241, 1);
}

/* 磨砂主题对话框按钮 */
[data-theme="frosted"] .btn-secondary {
    background: rgba(255, 255, 255, 0.3);
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

[data-theme="frosted"] .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.4);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.4);
}

[data-theme="frosted"] .btn-primary {
    background: rgba(99, 102, 241, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(99, 102, 241, 0.5);
}

[data-theme="frosted"] .btn-primary:hover {
    background: rgba(79, 70, 229, 1);
    border-color: rgba(79, 70, 229, 0.7);
}

/* 磨砂主题进度条 */
[data-theme="frosted"] .progress-bar {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

[data-theme="frosted"] .progress-fill {
    background: linear-gradient(90deg, 
        rgba(99, 102, 241, 0.8) 0%, 
        rgba(79, 70, 229, 0.9) 100%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 磨砂主题特殊效果 */
[data-theme="frosted"] .glass-effect {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(var(--blur-strength));
    -webkit-backdrop-filter: blur(var(--blur-strength));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
}

/* 磨砂主题沉浸式播放器 */
[data-theme="frosted"] .immersive-player {
    background: linear-gradient(135deg, 
        rgba(240, 240, 255, 0.95) 0%, 
        rgba(255, 240, 245, 0.95) 25%,
        rgba(245, 255, 240, 0.95) 50%,
        rgba(240, 245, 255, 0.95) 75%,
        rgba(255, 245, 240, 0.95) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
}

[data-theme="frosted"] .immersive-controls button {
    background: transparent;
    border: none;
    border-radius: 50%;
}

[data-theme="frosted"] .immersive-controls button:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 骨架屏动画变量 */
:root[data-theme="frosted"] {
    --skeleton-bg: rgba(255, 255, 255, 0.2);
    --skeleton-shimmer: rgba(255, 255, 255, 0.4);
}

/* 磨砂主题骨架屏 */
[data-theme="frosted"] .skeleton {
    background: linear-gradient(90deg, 
        var(--skeleton-bg) 25%, 
        var(--skeleton-shimmer) 50%, 
        var(--skeleton-bg) 75%);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 设置页面下拉列表磨砂浅色主题 */
[data-theme="frosted"] .settings-select {
    background: rgba(255, 255, 255, 0.25) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    color: var(--text-primary) !important;
    /* 磨砂浅色主题下拉箭头 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgb(71,85,105)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

[data-theme="frosted"] .settings-select:focus {
    background: rgba(255, 255, 255, 0.35) !important;
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
    outline: none;
}

[data-theme="frosted"] .settings-select:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.4);
}

/* 磨砂浅色主题下拉选项 */
[data-theme="frosted"] .settings-select option {
    background: rgba(255, 255, 255, 0.9) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px;
}

[data-theme="frosted"] .settings-select option:checked {
    background: var(--accent-color) !important;
    color: white !important;
}

[data-theme="frosted"] .settings-select option:hover {
    background: rgba(255, 255, 255, 0.7) !important;
    color: var(--text-primary) !important;
}
