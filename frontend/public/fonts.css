/* 字体管理系统 - 统一管理应用中的所有字体 */

/* ==================== 字体文件引入 ==================== */

/* 主要字体 - Inter (现代无衬线字体，适合界面) */
@font-face {
    font-family: "Inter";
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: local("Inter Light"), local("Inter-Light"),
         url("./fonts/Inter-Light.woff2") format("woff2"),
         url("./fonts/Inter-Light.woff") format("woff"),
         url("./fonts/Inter-Light.ttf") format("truetype");
}

@font-face {
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local("Inter Regular"), local("Inter-Regular"),
         url("./fonts/Inter-Regular.woff2") format("woff2"),
         url("./fonts/Inter-Regular.woff") format("woff"),
         url("./fonts/Inter-Medium.ttf") format("truetype");
}

@font-face {
    font-family: "Inter";
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: local("Inter Medium"), local("Inter-Medium"),
         url("./fonts/Inter-Medium.woff2") format("woff2"),
         url("./fonts/Inter-Medium.woff") format("woff"),
         url("./fonts/Inter-Medium.ttf") format("truetype");
}

@font-face {
    font-family: "Inter";
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: local("Inter SemiBold"), local("Inter-SemiBold"),
         url("./fonts/Inter-SemiBold.woff2") format("woff2"),
         url("./fonts/Inter-SemiBold.woff") format("woff"),
         url("./fonts/Inter-SemiBold.ttf") format("truetype");
}

@font-face {
    font-family: "Inter";
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: local("Inter Bold"), local("Inter-Bold"),
         url("./fonts/Inter-Bold.woff2") format("woff2"),
         url("./fonts/Inter-Bold.woff") format("woff"),
         url("./fonts/Inter-Bold.ttf") format("truetype");
}

/* 音乐专用字体 - Poppins (现代、友好的音乐应用字体) */
@font-face {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: local("Poppins Light"), local("Poppins-Light"),
         url("./fonts/Poppins-Light.woff2") format("woff2"),
         url("./fonts/Poppins-Light.woff") format("woff");
}

@font-face {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local("Poppins Regular"), local("Poppins-Regular"),
         url("./fonts/Poppins-Regular.woff2") format("woff2"),
         url("./fonts/Poppins-Regular.woff") format("woff");
}

@font-face {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: local("Poppins Medium"), local("Poppins-Medium"),
         url("./fonts/Poppins-Medium.woff2") format("woff2"),
         url("./fonts/Poppins-Medium.woff") format("woff");
}

@font-face {
    font-family: "Poppins";
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: local("Poppins SemiBold"), local("Poppins-SemiBold"),
         url("./fonts/Poppins-SemiBold.woff2") format("woff2"),
         url("./fonts/Poppins-SemiBold.woff") format("woff");
}

/* 等宽字体 - JetBrains Mono (用于代码、路径显示) */
@font-face {
    font-family: "JetBrains Mono";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local("JetBrains Mono Regular"), local("JetBrainsMono-Regular"),
         url("./fonts/JetBrainsMono-Regular.woff2") format("woff2"),
         url("./fonts/JetBrainsMono-Regular.woff") format("woff");
}

@font-face {
    font-family: "JetBrains Mono";
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: local("JetBrains Mono Medium"), local("JetBrainsMono-Medium"),
         url("./fonts/JetBrainsMono-Medium.woff2") format("woff2"),
         url("./fonts/JetBrainsMono-Medium.woff") format("woff");
}

/* 中文字体 - 思源黑体 (优秀的中文显示) */
@font-face {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: local("Source Han Sans CN Regular"), local("SourceHanSansCN-Regular"),
         url("./fonts/SourceHanSansCN-Regular.woff2") format("woff2"),
         url("./fonts/SourceHanSansCN-Regular.woff") format("woff");
}

@font-face {
    font-family: "Source Han Sans CN";
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: local("Source Han Sans CN Medium"), local("SourceHanSansCN-Medium"),
         url("./fonts/SourceHanSansCN-Medium.woff2") format("woff2"),
         url("./fonts/SourceHanSansCN-Medium.woff") format("woff");
}

/* ==================== 字体变量定义 ==================== */

:root {
    /* 主要字体栈 */
    --font-primary: "Inter", "Source Han Sans CN", -apple-system, BlinkMacSystemFont, 
                    "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 
                    "Helvetica Neue", Arial, sans-serif;
    
    /* 音乐专用字体栈 */
    --font-music: "Poppins", "Source Han Sans CN", -apple-system, BlinkMacSystemFont,
                  "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
                  "Helvetica Neue", Arial, sans-serif;
    
    /* 等宽字体栈 */
    --font-mono: "JetBrains Mono", "SF Mono", Monaco, "Cascadia Code", "Roboto Mono",
                 Consolas, "Courier New", monospace;
    
    /* 中文优先字体栈 */
    --font-chinese: "Source Han Sans CN", "PingFang SC", "Hiragino Sans GB", 
                    "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
    
    /* 字体大小变量 */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    
    /* 行高变量 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* 字重变量 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

/* ==================== 字体应用类 ==================== */

/* 主要字体类 */
.font-primary {
    font-family: var(--font-primary);
}

.font-music {
    font-family: var(--font-music);
}

.font-mono {
    font-family: var(--font-mono);
}

.font-chinese {
    font-family: var(--font-chinese);
}

/* 字体大小类 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

/* 字重类 */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 行高类 */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* ==================== 特定用途字体样式 ==================== */

/* 标题字体 */
.title-font {
    font-family: var(--font-music);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
}

/* 歌曲名称字体 */
.song-title-font {
    font-family: var(--font-music);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
}

/* 艺术家名称字体 */
.artist-font {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
}

/* 路径显示字体 */
.path-font {
    font-family: var(--font-mono);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-sm);
}

/* 歌词字体 */
.lyrics-font {
    font-family: var(--font-chinese);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
}

/* 按钮字体 */
.button-font {
    font-family: var(--font-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
}

/* 代码字体 */
.code-font {
    font-family: var(--font-mono);
    font-weight: var(--font-weight-normal);
}

/* ==================== 字体优化设置 ==================== */

/* 全局字体优化 */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 中文字体优化 */
:lang(zh) {
    font-family: var(--font-chinese);
    text-rendering: optimizeLegibility;
}

/* 数字字体优化 */
.font-tabular {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
}

/* 字体回退优化 */
@supports not (font-display: swap) {
    @font-face {
        font-family: "Inter";
        font-display: block;
        src: local("Inter"), local("Inter-Regular");
    }
}
