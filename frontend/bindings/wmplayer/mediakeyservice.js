// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * MediaKeyService 媒体键服务
 * 由于 Wails 3 可能不支持全局快捷键，我们使用前端键盘事件监听
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

/**
 * GetMediaKeyStatus 获取媒体键状态
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function GetMediaKeyStatus() {
    return $Call.ByID(717811311).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * RegisterMediaKeys 注册媒体键（前端实现）
 * @returns {$CancellablePromise<void>}
 */
export function RegisterMediaKeys() {
    return $Call.ByID(1820877813);
}

/**
 * SetApp 设置应用实例（保持兼容性）
 * @param {any} app
 * @returns {$CancellablePromise<void>}
 */
export function SetApp(app) {
    return $Call.ByID(1463270849, app);
}

/**
 * SetContext 设置上下文
 * @returns {$CancellablePromise<void>}
 */
export function SetContext() {
    return $Call.ByID(2357162989);
}

/**
 * UnregisterMediaKeys 取消注册媒体键
 * @returns {$CancellablePromise<void>}
 */
export function UnregisterMediaKeys() {
    return $Call.ByID(1210209780);
}

// Private type creation functions
const $$createType0 = $Create.Map($Create.Any, $Create.Any);
