// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * AlbumService 专辑服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * GetAlbumDetail 获取专辑详情
 * @param {string} albumID
 * @returns {$CancellablePromise<$models.AlbumDetailResponse>}
 */
export function GetAlbumDetail(albumID) {
    return $Call.ByID(1966691988, albumID).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * GetAlbumSongs 获取专辑歌曲列表
 * @param {string} albumID
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.AlbumSongsResponse>}
 */
export function GetAlbumSongs(albumID, page, pageSize) {
    return $Call.ByID(2460318245, albumID, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetPlaylistDetail 获取歌单详情
 * @param {string} playlistID
 * @returns {$CancellablePromise<$models.AlbumDetailResponse>}
 */
export function GetPlaylistDetail(playlistID) {
    return $Call.ByID(669809681, playlistID).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * GetPlaylistSongs 获取歌单歌曲列表
 * @param {string} playlistID
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.AlbumSongsResponse>}
 */
export function GetPlaylistSongs(playlistID, page, pageSize) {
    return $Call.ByID(3618309342, playlistID, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.AlbumDetailResponse.createFrom;
const $$createType1 = $models.AlbumSongsResponse.createFrom;
