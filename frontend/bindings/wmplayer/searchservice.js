// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * SearchService 搜索服务结构体
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * GetHotSearch 获取热搜列表
 * @returns {$CancellablePromise<$models.HotSearchResponse>}
 */
export function GetHotSearch() {
    return $Call.ByID(4119922366).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetSearchSuggest 获取搜索建议
 * @param {string} keyword
 * @returns {$CancellablePromise<$models.SearchSuggestResponse>}
 */
export function GetSearchSuggest(keyword) {
    return $Call.ByID(156124283, keyword).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType4($result);
    }));
}

/**
 * Search 综合搜索
 * @param {string} keyword
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.SearchResponse>}
 */
export function Search(keyword, page, pageSize) {
    return $Call.ByID(4153828849, keyword, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

/**
 * SearchAlbums 搜索专辑
 * @param {string} keyword
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.SearchResponse>}
 */
export function SearchAlbums(keyword, page, pageSize) {
    return $Call.ByID(2634631457, keyword, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

/**
 * SearchArtists 搜索艺人
 * @param {string} keyword
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.SearchResponse>}
 */
export function SearchArtists(keyword, page, pageSize) {
    return $Call.ByID(1617328731, keyword, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

/**
 * SearchMVs 搜索MV
 * @param {string} keyword
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.SearchResponse>}
 */
export function SearchMVs(keyword, page, pageSize) {
    return $Call.ByID(3813653295, keyword, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

/**
 * SearchPlaylists 搜索歌单
 * @param {string} keyword
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.SearchResponse>}
 */
export function SearchPlaylists(keyword, page, pageSize) {
    return $Call.ByID(2306380212, keyword, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

/**
 * SearchSongs 搜索歌曲
 * @param {string} keyword
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.SearchResponse>}
 */
export function SearchSongs(keyword, page, pageSize) {
    return $Call.ByID(3129591841, keyword, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.HotSearchData.createFrom;
const $$createType1 = $models.ApiResponse.createFrom($$createType0);
const $$createType2 = $models.SearchSuggestData.createFrom;
const $$createType3 = $Create.Array($$createType2);
const $$createType4 = $models.ApiResponse.createFrom($$createType3);
const $$createType5 = $models.SearchResults.createFrom;
const $$createType6 = $models.ApiResponse.createFrom($$createType5);
