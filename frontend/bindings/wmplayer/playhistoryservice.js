// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * PlayHistoryService 播放历史服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * AddPlayHistory 添加播放历史记录
 * @param {$models.AddPlayHistoryRequest} request
 * @returns {$CancellablePromise<$models.PlayHistoryResponse>}
 */
export function AddPlayHistory(request) {
    return $Call.ByID(3581009760, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * ClearPlayHistory 清空播放历史
 * @returns {$CancellablePromise<$models.PlayHistoryResponse>}
 */
export function ClearPlayHistory() {
    return $Call.ByID(2986802494).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetPlayHistory 获取播放历史
 * @param {$models.GetPlayHistoryRequest} request
 * @returns {$CancellablePromise<$models.PlayHistoryResponse>}
 */
export function GetPlayHistory(request) {
    return $Call.ByID(3767242453, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.PlayHistoryData.createFrom;
const $$createType1 = $models.ApiResponse.createFrom($$createType0);
