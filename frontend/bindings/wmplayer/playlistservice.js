// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * PlaylistService 播放列表服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * AddToPlaylist 添加歌曲到播放列表
 * @param {$models.AddToPlaylistRequest} request
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function AddToPlaylist(request) {
    return $Call.ByID(2074831381, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * ClearPlaylist 清空播放列表
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function ClearPlaylist() {
    return $Call.ByID(1596004154).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetNextSong 获取下一首歌曲
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function GetNextSong() {
    return $Call.ByID(776240129).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetPlaylist 获取当前播放列表
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function GetPlaylist() {
    return $Call.ByID(3588046843).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetPreviousSong 获取上一首歌曲
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function GetPreviousSong() {
    return $Call.ByID(3098767777).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * RemoveFromPlaylist 从播放列表移除歌曲
 * @param {string} hash
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function RemoveFromPlaylist(hash) {
    return $Call.ByID(2132986749, hash).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * SetCurrentIndex 设置当前播放索引
 * @param {number} index
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function SetCurrentIndex(index) {
    return $Call.ByID(1264172618, index).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * SetPlaylist 设置播放列表
 * @param {$models.SetPlaylistRequest} request
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function SetPlaylist(request) {
    return $Call.ByID(781795207, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * UpdatePlayMode 更新播放模式
 * @param {$models.UpdatePlayModeRequest} request
 * @returns {$CancellablePromise<$models.PlayerPlaylistResponse>}
 */
export function UpdatePlayMode(request) {
    return $Call.ByID(558594727, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.PlayerPlaylistData.createFrom;
const $$createType1 = $models.ApiResponse.createFrom($$createType0);
