// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * DownloadService 下载服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * AddDownloadRecord 添加下载记录
 * @param {$models.AddDownloadRecordRequest} request
 * @returns {$CancellablePromise<$models.DownloadRecordsResponse>}
 */
export function AddDownloadRecord(request) {
    return $Call.ByID(2276979065, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * ClearDownloadRecords 清空下载记录
 * @returns {$CancellablePromise<$models.DownloadRecordsResponse>}
 */
export function ClearDownloadRecords() {
    return $Call.ByID(2793156428).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * DeleteDownloadRecord 删除下载记录
 * @param {$models.DeleteDownloadRecordRequest} request
 * @returns {$CancellablePromise<$models.DownloadRecordsResponse>}
 */
export function DeleteDownloadRecord(request) {
    return $Call.ByID(2383175215, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetDownloadRecords 获取下载记录
 * @param {$models.GetDownloadRecordsRequest} request
 * @returns {$CancellablePromise<$models.DownloadRecordsResponse>}
 */
export function GetDownloadRecords(request) {
    return $Call.ByID(1818881875, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * OpenFileFolder 打开文件所在文件夹
 * @param {string} filePath
 * @returns {$CancellablePromise<$models.ApiResponse<string>>}
 */
export function OpenFileFolder(filePath) {
    return $Call.ByID(781696475, filePath).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType2($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.DownloadRecordsData.createFrom;
const $$createType1 = $models.ApiResponse.createFrom($$createType0);
const $$createType2 = $models.ApiResponse.createFrom($Create.Any);
