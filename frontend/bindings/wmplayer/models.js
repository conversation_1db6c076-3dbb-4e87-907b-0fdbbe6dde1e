// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as time$0 from "../time/models.js";

/**
 * AIRecommendData AI推荐歌曲数据结构
 */
export class AIRecommendData {
    /**
     * Creates a new AIRecommendData instance.
     * @param {Partial<AIRecommendData>} [$$source = {}] - The source object to create the AIRecommendData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AIRecommendData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AIRecommendData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AIRecommendData(/** @type {Partial<AIRecommendData>} */($$parsedSource));
    }
}

/**
 * AddDownloadRecordRequest 添加下载记录请求
 */
export class AddDownloadRecordRequest {
    /**
     * Creates a new AddDownloadRecordRequest instance.
     * @param {Partial<AddDownloadRecordRequest>} [$$source = {}] - The source object to create the AddDownloadRecordRequest.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("artist_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["artist_name"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("file_path" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["file_path"] = "";
        }
        if (!("file_size" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["file_size"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AddDownloadRecordRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AddDownloadRecordRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AddDownloadRecordRequest(/** @type {Partial<AddDownloadRecordRequest>} */($$parsedSource));
    }
}

/**
 * AddFavoriteRequest 添加收藏请求结构
 */
export class AddFavoriteRequest {
    /**
     * Creates a new AddFavoriteRequest instance.
     * @param {Partial<AddFavoriteRequest>} [$$source = {}] - The source object to create the AddFavoriteRequest.
     */
    constructor($$source = {}) {
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AddFavoriteRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AddFavoriteRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AddFavoriteRequest(/** @type {Partial<AddFavoriteRequest>} */($$parsedSource));
    }
}

/**
 * AddFavoriteResponse 添加收藏响应结构
 */
export class AddFavoriteResponse {
    /**
     * Creates a new AddFavoriteResponse instance.
     * @param {Partial<AddFavoriteResponse>} [$$source = {}] - The source object to create the AddFavoriteResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (!("error_code" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["error_code"] = 0;
        }
        if (!("status" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["status"] = 0;
        }
        if (!("data" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["data"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AddFavoriteResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AddFavoriteResponse}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AddFavoriteResponse(/** @type {Partial<AddFavoriteResponse>} */($$parsedSource));
    }
}

/**
 * AddPlayHistoryRequest 添加播放历史请求
 */
export class AddPlayHistoryRequest {
    /**
     * Creates a new AddPlayHistoryRequest instance.
     * @param {Partial<AddPlayHistoryRequest>} [$$source = {}] - The source object to create the AddPlayHistoryRequest.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AddPlayHistoryRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AddPlayHistoryRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AddPlayHistoryRequest(/** @type {Partial<AddPlayHistoryRequest>} */($$parsedSource));
    }
}

/**
 * AddToPlaylistRequest 添加到播放列表请求
 */
export class AddToPlaylistRequest {
    /**
     * Creates a new AddToPlaylistRequest instance.
     * @param {Partial<AddToPlaylistRequest>} [$$source = {}] - The source object to create the AddToPlaylistRequest.
     */
    constructor($$source = {}) {
        if (!("song" in $$source)) {
            /**
             * 要添加的歌曲
             * @member
             * @type {PlayerPlaylistSong}
             */
            this["song"] = (new PlayerPlaylistSong());
        }
        if (!("insert" in $$source)) {
            /**
             * 是否插入到当前位置后面
             * @member
             * @type {boolean}
             */
            this["insert"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AddToPlaylistRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AddToPlaylistRequest}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType0;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("song" in $$parsedSource) {
            $$parsedSource["song"] = $$createField0_0($$parsedSource["song"]);
        }
        return new AddToPlaylistRequest(/** @type {Partial<AddToPlaylistRequest>} */($$parsedSource));
    }
}

/**
 * AlbumDetailData 专辑详情数据结构
 */
export class AlbumDetailData {
    /**
     * Creates a new AlbumDetailData instance.
     * @param {Partial<AlbumDetailData>} [$$source = {}] - The source object to create the AlbumDetailData.
     */
    constructor($$source = {}) {
        if (!("id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["id"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("publish_date" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["publish_date"] = "";
        }
        if (!("song_count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["song_count"] = 0;
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("description" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["description"] = "";
        }
        if (!("publish_company" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["publish_company"] = "";
        }
        if (!("language" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["language"] = "";
        }
        if (!("category" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["category"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AlbumDetailData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AlbumDetailData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AlbumDetailData(/** @type {Partial<AlbumDetailData>} */($$parsedSource));
    }
}

/**
 * AlbumDetailResponse 专辑详情响应
 */
export class AlbumDetailResponse {
    /**
     * Creates a new AlbumDetailResponse instance.
     * @param {Partial<AlbumDetailResponse>} [$$source = {}] - The source object to create the AlbumDetailResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (!("error_code" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["error_code"] = 0;
        }
        if (!("status" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["status"] = 0;
        }
        if (!("data" in $$source)) {
            /**
             * @member
             * @type {AlbumDetailData}
             */
            this["data"] = (new AlbumDetailData());
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AlbumDetailResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AlbumDetailResponse}
     */
    static createFrom($$source = {}) {
        const $$createField4_0 = $$createType1;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("data" in $$parsedSource) {
            $$parsedSource["data"] = $$createField4_0($$parsedSource["data"]);
        }
        return new AlbumDetailResponse(/** @type {Partial<AlbumDetailResponse>} */($$parsedSource));
    }
}

/**
 * AlbumSongData 专辑歌曲数据结构
 */
export class AlbumSongData {
    /**
     * Creates a new AlbumSongData instance.
     * @param {Partial<AlbumSongData>} [$$source = {}] - The source object to create the AlbumSongData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AlbumSongData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AlbumSongData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AlbumSongData(/** @type {Partial<AlbumSongData>} */($$parsedSource));
    }
}

/**
 * AlbumSongsResponse 专辑歌曲列表响应
 */
export class AlbumSongsResponse {
    /**
     * Creates a new AlbumSongsResponse instance.
     * @param {Partial<AlbumSongsResponse>} [$$source = {}] - The source object to create the AlbumSongsResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (!("error_code" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["error_code"] = 0;
        }
        if (!("status" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["status"] = 0;
        }
        if (!("data" in $$source)) {
            /**
             * @member
             * @type {AlbumSongData[]}
             */
            this["data"] = [];
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AlbumSongsResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AlbumSongsResponse}
     */
    static createFrom($$source = {}) {
        const $$createField4_0 = $$createType3;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("data" in $$parsedSource) {
            $$parsedSource["data"] = $$createField4_0($$parsedSource["data"]);
        }
        return new AlbumSongsResponse(/** @type {Partial<AlbumSongsResponse>} */($$parsedSource));
    }
}

/**
 * ApiResponse 通用API响应结构体
 * @template T
 */
export class ApiResponse {
    /**
     * Creates a new ApiResponse instance.
     * @param {Partial<ApiResponse<T>>} [$$source = {}] - The source object to create the ApiResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {number | undefined}
             */
            this["error_code"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {number | undefined}
             */
            this["status"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {T | undefined}
             */
            this["data"] = undefined;
        }

        Object.assign(this, $$source);
    }

    /**
     * Given creation functions for each type parameter,
     * returns a creation function for a concrete instance
     * of the generic class ApiResponse.
     * @template [T=any]
     * @param {(source: any) => T} $$createParamT
     * @returns {($$source?: any) => ApiResponse<T>}
     */
    static createFrom($$createParamT) {
        const $$createField4_0 = $$createParamT;
        return ($$source = {}) => {
            let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
            if ("data" in $$parsedSource) {
                $$parsedSource["data"] = $$createField4_0($$parsedSource["data"]);
            }
            return new ApiResponse(/** @type {Partial<ApiResponse<T>>} */($$parsedSource));
        };
    }
}

/**
 * AIRecommendResponse AI推荐歌曲响应结构
 */
export const AIRecommendResponse = ApiResponse;

/**
 * AIRecommendResponse AI推荐歌曲响应结构
 * @typedef {ApiResponse<AIRecommendData[]>} AIRecommendResponse
 */

/**
 * AudioFileResponse 音频文件响应结构
 */
export class AudioFileResponse {
    /**
     * Creates a new AudioFileResponse instance.
     * @param {Partial<AudioFileResponse>} [$$source = {}] - The source object to create the AudioFileResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (/** @type {any} */(false)) {
            /**
             * 音频文件二进制数据
             * @member
             * @type {string | undefined}
             */
            this["data"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * MIME类型
             * @member
             * @type {string | undefined}
             */
            this["mimeType"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 文件名
             * @member
             * @type {string | undefined}
             */
            this["fileName"] = undefined;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AudioFileResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {AudioFileResponse}
     */
    static createFrom($$source = {}) {
        const $$createField2_0 = $Create.ByteSlice;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("data" in $$parsedSource) {
            $$parsedSource["data"] = $$createField2_0($$parsedSource["data"]);
        }
        return new AudioFileResponse(/** @type {Partial<AudioFileResponse>} */($$parsedSource));
    }
}

/**
 * BehaviorSettings 应用行为设置
 */
export class BehaviorSettings {
    /**
     * Creates a new BehaviorSettings instance.
     * @param {Partial<BehaviorSettings>} [$$source = {}] - The source object to create the BehaviorSettings.
     */
    constructor($$source = {}) {
        if (!("closeAction" in $$source)) {
            /**
             * "ask", "minimize", "exit"
             * @member
             * @type {string}
             */
            this["closeAction"] = "";
        }
        if (!("startMinimized" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["startMinimized"] = false;
        }
        if (!("autoStart" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["autoStart"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new BehaviorSettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {BehaviorSettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new BehaviorSettings(/** @type {Partial<BehaviorSettings>} */($$parsedSource));
    }
}

/**
 * CacheResponse 缓存服务响应
 */
export class CacheResponse {
    /**
     * Creates a new CacheResponse instance.
     * @param {Partial<CacheResponse>} [$$source = {}] - The source object to create the CacheResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {string | undefined}
             */
            this["data"] = undefined;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new CacheResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {CacheResponse}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new CacheResponse(/** @type {Partial<CacheResponse>} */($$parsedSource));
    }
}

/**
 * CaptchaData 验证码响应的数据结构
 */
export class CaptchaData {
    /**
     * Creates a new CaptchaData instance.
     * @param {Partial<CaptchaData>} [$$source = {}] - The source object to create the CaptchaData.
     */
    constructor($$source = {}) {
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {number | undefined}
             */
            this["count"] = undefined;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new CaptchaData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {CaptchaData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new CaptchaData(/** @type {Partial<CaptchaData>} */($$parsedSource));
    }
}

/**
 * CaptchaResponse 验证码响应结构 (保持向后兼容)
 */
export const CaptchaResponse = ApiResponse;

/**
 * CaptchaResponse 验证码响应结构 (保持向后兼容)
 * @typedef {ApiResponse<CaptchaData>} CaptchaResponse
 */

/**
 * DailyRecommendData 每日推荐歌曲数据结构
 */
export class DailyRecommendData {
    /**
     * Creates a new DailyRecommendData instance.
     * @param {Partial<DailyRecommendData>} [$$source = {}] - The source object to create the DailyRecommendData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new DailyRecommendData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {DailyRecommendData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new DailyRecommendData(/** @type {Partial<DailyRecommendData>} */($$parsedSource));
    }
}

/**
 * DailyRecommendResponse 每日推荐响应结构
 */
export const DailyRecommendResponse = ApiResponse;

/**
 * DailyRecommendResponse 每日推荐响应结构
 * @typedef {ApiResponse<DailyRecommendData[]>} DailyRecommendResponse
 */

/**
 * DeleteDownloadRecordRequest 删除下载记录请求
 */
export class DeleteDownloadRecordRequest {
    /**
     * Creates a new DeleteDownloadRecordRequest instance.
     * @param {Partial<DeleteDownloadRecordRequest>} [$$source = {}] - The source object to create the DeleteDownloadRecordRequest.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new DeleteDownloadRecordRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {DeleteDownloadRecordRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new DeleteDownloadRecordRequest(/** @type {Partial<DeleteDownloadRecordRequest>} */($$parsedSource));
    }
}

/**
 * DownloadRecord 下载记录
 */
export class DownloadRecord {
    /**
     * Creates a new DownloadRecord instance.
     * @param {Partial<DownloadRecord>} [$$source = {}] - The source object to create the DownloadRecord.
     */
    constructor($$source = {}) {
        if (!("id" in $$source)) {
            /**
             * 记录ID（使用歌曲hash）
             * @member
             * @type {string}
             */
            this["id"] = "";
        }
        if (!("hash" in $$source)) {
            /**
             * 歌曲hash
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * 歌曲名称
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("artist_name" in $$source)) {
            /**
             * 艺术家名称
             * @member
             * @type {string}
             */
            this["artist_name"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * 文件名
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("download_time" in $$source)) {
            /**
             * 下载时间
             * @member
             * @type {time$0.Time}
             */
            this["download_time"] = null;
        }
        if (!("file_path" in $$source)) {
            /**
             * 文件路径
             * @member
             * @type {string}
             */
            this["file_path"] = "";
        }
        if (!("file_size" in $$source)) {
            /**
             * 文件大小（字节）
             * @member
             * @type {number}
             */
            this["file_size"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new DownloadRecord instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {DownloadRecord}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new DownloadRecord(/** @type {Partial<DownloadRecord>} */($$parsedSource));
    }
}

/**
 * DownloadRecordsData 下载记录数据结构
 */
export class DownloadRecordsData {
    /**
     * Creates a new DownloadRecordsData instance.
     * @param {Partial<DownloadRecordsData>} [$$source = {}] - The source object to create the DownloadRecordsData.
     */
    constructor($$source = {}) {
        if (!("records" in $$source)) {
            /**
             * 下载记录列表
             * @member
             * @type {DownloadRecord[]}
             */
            this["records"] = [];
        }
        if (!("total_count" in $$source)) {
            /**
             * 总记录数
             * @member
             * @type {number}
             */
            this["total_count"] = 0;
        }
        if (!("update_time" in $$source)) {
            /**
             * 更新时间
             * @member
             * @type {time$0.Time}
             */
            this["update_time"] = null;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new DownloadRecordsData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {DownloadRecordsData}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType5;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("records" in $$parsedSource) {
            $$parsedSource["records"] = $$createField0_0($$parsedSource["records"]);
        }
        return new DownloadRecordsData(/** @type {Partial<DownloadRecordsData>} */($$parsedSource));
    }
}

/**
 * DownloadRecordsResponse 下载记录响应结构
 */
export const DownloadRecordsResponse = ApiResponse;

/**
 * DownloadRecordsResponse 下载记录响应结构
 * @typedef {ApiResponse<DownloadRecordsData>} DownloadRecordsResponse
 */

/**
 * DownloadSettings 下载设置
 */
export class DownloadSettings {
    /**
     * Creates a new DownloadSettings instance.
     * @param {Partial<DownloadSettings>} [$$source = {}] - The source object to create the DownloadSettings.
     */
    constructor($$source = {}) {
        if (!("downloadPath" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["downloadPath"] = "";
        }
        if (!("autoDownload" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["autoDownload"] = false;
        }
        if (!("downloadLyrics" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["downloadLyrics"] = false;
        }
        if (!("downloadCover" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["downloadCover"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new DownloadSettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {DownloadSettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new DownloadSettings(/** @type {Partial<DownloadSettings>} */($$parsedSource));
    }
}

/**
 * FavoritesSongData 我喜欢的歌曲数据结构
 */
export class FavoritesSongData {
    /**
     * Creates a new FavoritesSongData instance.
     * @param {Partial<FavoritesSongData>} [$$source = {}] - The source object to create the FavoritesSongData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("mixsongid" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["mixsongid"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new FavoritesSongData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {FavoritesSongData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new FavoritesSongData(/** @type {Partial<FavoritesSongData>} */($$parsedSource));
    }
}

/**
 * FavoritesSongResponse 我喜欢的歌曲响应结构
 */
export const FavoritesSongResponse = ApiResponse;

/**
 * FavoritesSongResponse 我喜欢的歌曲响应结构
 * @typedef {ApiResponse<FavoritesSongData[]>} FavoritesSongResponse
 */

/**
 * FmRequestParams 私人FM请求参数
 */
export class FmRequestParams {
    /**
     * Creates a new FmRequestParams instance.
     * @param {Partial<FmRequestParams>} [$$source = {}] - The source object to create the FmRequestParams.
     */
    constructor($$source = {}) {
        if (/** @type {any} */(false)) {
            /**
             * 音乐 hash
             * @member
             * @type {string | undefined}
             */
            this["hash"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 音乐 songid
             * @member
             * @type {string | undefined}
             */
            this["songid"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 已播放时间
             * @member
             * @type {number | undefined}
             */
            this["playtime"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 获取模式：normal, small, peak
             * @member
             * @type {string | undefined}
             */
            this["mode"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 操作：play, garbage
             * @member
             * @type {string | undefined}
             */
            this["action"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * AI模式：0=Alpha, 1=Beta, 2=Gamma
             * @member
             * @type {number | undefined}
             */
            this["song_pool_id"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 是否已播放完成
             * @member
             * @type {boolean | undefined}
             */
            this["is_overplay"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * 剩余未播放歌曲数
             * @member
             * @type {number | undefined}
             */
            this["remain_songcnt"] = undefined;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new FmRequestParams instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {FmRequestParams}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new FmRequestParams(/** @type {Partial<FmRequestParams>} */($$parsedSource));
    }
}

/**
 * FmResponse 私人FM响应结构
 */
export const FmResponse = ApiResponse;

/**
 * FmResponse 私人FM响应结构
 * @typedef {ApiResponse<FmSongData[]>} FmResponse
 */

/**
 * FmSongData 私人FM歌曲数据结构
 */
export class FmSongData {
    /**
     * Creates a new FmSongData instance.
     * @param {Partial<FmSongData>} [$$source = {}] - The source object to create the FmSongData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("song_url" in $$source)) {
            /**
             * @member
             * @type {string[]}
             */
            this["song_url"] = [];
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new FmSongData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {FmSongData}
     */
    static createFrom($$source = {}) {
        const $$createField8_0 = $$createType6;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("song_url" in $$parsedSource) {
            $$parsedSource["song_url"] = $$createField8_0($$parsedSource["song_url"]);
        }
        return new FmSongData(/** @type {Partial<FmSongData>} */($$parsedSource));
    }
}

/**
 * FolderSelectResponse 文件夹选择响应
 */
export class FolderSelectResponse {
    /**
     * Creates a new FolderSelectResponse instance.
     * @param {Partial<FolderSelectResponse>} [$$source = {}] - The source object to create the FolderSelectResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (!("path" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["path"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new FolderSelectResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {FolderSelectResponse}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new FolderSelectResponse(/** @type {Partial<FolderSelectResponse>} */($$parsedSource));
    }
}

/**
 * GetDownloadRecordsRequest 获取下载记录请求
 */
export class GetDownloadRecordsRequest {
    /**
     * Creates a new GetDownloadRecordsRequest instance.
     * @param {Partial<GetDownloadRecordsRequest>} [$$source = {}] - The source object to create the GetDownloadRecordsRequest.
     */
    constructor($$source = {}) {
        if (!("page" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["page"] = 0;
        }
        if (!("page_size" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["page_size"] = 0;
        }
        if (!("filter" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filter"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new GetDownloadRecordsRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {GetDownloadRecordsRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new GetDownloadRecordsRequest(/** @type {Partial<GetDownloadRecordsRequest>} */($$parsedSource));
    }
}

/**
 * GetPlayHistoryRequest 获取播放历史请求
 */
export class GetPlayHistoryRequest {
    /**
     * Creates a new GetPlayHistoryRequest instance.
     * @param {Partial<GetPlayHistoryRequest>} [$$source = {}] - The source object to create the GetPlayHistoryRequest.
     */
    constructor($$source = {}) {
        if (!("page" in $$source)) {
            /**
             * 页码
             * @member
             * @type {number}
             */
            this["page"] = 0;
        }
        if (!("page_size" in $$source)) {
            /**
             * 每页数量
             * @member
             * @type {number}
             */
            this["page_size"] = 0;
        }
        if (!("filter" in $$source)) {
            /**
             * 过滤条件：all, today, yesterday, week
             * @member
             * @type {string}
             */
            this["filter"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new GetPlayHistoryRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {GetPlayHistoryRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new GetPlayHistoryRequest(/** @type {Partial<GetPlayHistoryRequest>} */($$parsedSource));
    }
}

/**
 * HotSearchCategory 热搜分类数据结构
 */
export class HotSearchCategory {
    /**
     * Creates a new HotSearchCategory instance.
     * @param {Partial<HotSearchCategory>} [$$source = {}] - The source object to create the HotSearchCategory.
     */
    constructor($$source = {}) {
        if (!("name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["name"] = "";
        }
        if (!("keywords" in $$source)) {
            /**
             * @member
             * @type {HotSearchKeyword[]}
             */
            this["keywords"] = [];
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new HotSearchCategory instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {HotSearchCategory}
     */
    static createFrom($$source = {}) {
        const $$createField1_0 = $$createType8;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("keywords" in $$parsedSource) {
            $$parsedSource["keywords"] = $$createField1_0($$parsedSource["keywords"]);
        }
        return new HotSearchCategory(/** @type {Partial<HotSearchCategory>} */($$parsedSource));
    }
}

/**
 * HotSearchData 热搜数据结构
 */
export class HotSearchData {
    /**
     * Creates a new HotSearchData instance.
     * @param {Partial<HotSearchData>} [$$source = {}] - The source object to create the HotSearchData.
     */
    constructor($$source = {}) {
        if (!("timestamp" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["timestamp"] = 0;
        }
        if (!("list" in $$source)) {
            /**
             * @member
             * @type {HotSearchCategory[]}
             */
            this["list"] = [];
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new HotSearchData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {HotSearchData}
     */
    static createFrom($$source = {}) {
        const $$createField1_0 = $$createType10;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("list" in $$parsedSource) {
            $$parsedSource["list"] = $$createField1_0($$parsedSource["list"]);
        }
        return new HotSearchData(/** @type {Partial<HotSearchData>} */($$parsedSource));
    }
}

/**
 * HotSearchKeyword 热搜关键词数据结构
 */
export class HotSearchKeyword {
    /**
     * Creates a new HotSearchKeyword instance.
     * @param {Partial<HotSearchKeyword>} [$$source = {}] - The source object to create the HotSearchKeyword.
     */
    constructor($$source = {}) {
        if (!("reason" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["reason"] = "";
        }
        if (!("json_url" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["json_url"] = "";
        }
        if (!("jumpurl" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["jumpurl"] = "";
        }
        if (!("keyword" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["keyword"] = "";
        }
        if (!("is_cover_word" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["is_cover_word"] = 0;
        }
        if (!("type" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["type"] = 0;
        }
        if (!("icon" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["icon"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new HotSearchKeyword instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {HotSearchKeyword}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new HotSearchKeyword(/** @type {Partial<HotSearchKeyword>} */($$parsedSource));
    }
}

/**
 * HotSearchResponse 热搜响应结构
 */
export const HotSearchResponse = ApiResponse;

/**
 * HotSearchResponse 热搜响应结构
 * @typedef {ApiResponse<HotSearchData>} HotSearchResponse
 */

/**
 * HotkeysSettings 快捷键设置
 */
export class HotkeysSettings {
    /**
     * Creates a new HotkeysSettings instance.
     * @param {Partial<HotkeysSettings>} [$$source = {}] - The source object to create the HotkeysSettings.
     */
    constructor($$source = {}) {
        if (!("playPause" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["playPause"] = "";
        }
        if (!("nextTrack" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["nextTrack"] = "";
        }
        if (!("prevTrack" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["prevTrack"] = "";
        }
        if (!("volumeUp" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["volumeUp"] = "";
        }
        if (!("volumeDown" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["volumeDown"] = "";
        }
        if (!("toggleLyrics" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["toggleLyrics"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new HotkeysSettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {HotkeysSettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new HotkeysSettings(/** @type {Partial<HotkeysSettings>} */($$parsedSource));
    }
}

/**
 * InterfaceSettings 界面设置
 */
export class InterfaceSettings {
    /**
     * Creates a new InterfaceSettings instance.
     * @param {Partial<InterfaceSettings>} [$$source = {}] - The source object to create the InterfaceSettings.
     */
    constructor($$source = {}) {
        if (!("theme" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["theme"] = "";
        }
        if (!("language" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["language"] = "";
        }
        if (!("showLyrics" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["showLyrics"] = false;
        }
        if (!("showSpectrum" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["showSpectrum"] = false;
        }
        if (!("miniPlayer" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["miniPlayer"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new InterfaceSettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {InterfaceSettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new InterfaceSettings(/** @type {Partial<InterfaceSettings>} */($$parsedSource));
    }
}

/**
 * LocalMusicFile 本地音乐文件信息
 */
export class LocalMusicFile {
    /**
     * Creates a new LocalMusicFile instance.
     * @param {Partial<LocalMusicFile>} [$$source = {}] - The source object to create the LocalMusicFile.
     */
    constructor($$source = {}) {
        if (!("file_path" in $$source)) {
            /**
             * 文件路径
             * @member
             * @type {string}
             */
            this["file_path"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * 文件名
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("title" in $$source)) {
            /**
             * 歌曲标题
             * @member
             * @type {string}
             */
            this["title"] = "";
        }
        if (!("artist" in $$source)) {
            /**
             * 艺术家
             * @member
             * @type {string}
             */
            this["artist"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * 专辑
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("year" in $$source)) {
            /**
             * 年份
             * @member
             * @type {number}
             */
            this["year"] = 0;
        }
        if (!("genre" in $$source)) {
            /**
             * 流派
             * @member
             * @type {string}
             */
            this["genre"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * 时长(秒)
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("bitrate" in $$source)) {
            /**
             * 比特率
             * @member
             * @type {number}
             */
            this["bitrate"] = 0;
        }
        if (!("file_size" in $$source)) {
            /**
             * 文件大小
             * @member
             * @type {number}
             */
            this["file_size"] = 0;
        }
        if (!("format" in $$source)) {
            /**
             * 文件格式
             * @member
             * @type {string}
             */
            this["format"] = "";
        }
        if (!("hash" in $$source)) {
            /**
             * 文件哈希值
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("last_modified" in $$source)) {
            /**
             * 最后修改时间
             * @member
             * @type {number}
             */
            this["last_modified"] = 0;
        }
        if (!("union_cover" in $$source)) {
            /**
             * 封面图片URL
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("lyrics" in $$source)) {
            /**
             * 歌词内容
             * @member
             * @type {string}
             */
            this["lyrics"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new LocalMusicFile instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {LocalMusicFile}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new LocalMusicFile(/** @type {Partial<LocalMusicFile>} */($$parsedSource));
    }
}

/**
 * LocalMusicResponse 本地音乐响应结构
 */
export class LocalMusicResponse {
    /**
     * Creates a new LocalMusicResponse instance.
     * @param {Partial<LocalMusicResponse>} [$$source = {}] - The source object to create the LocalMusicResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (!("data" in $$source)) {
            /**
             * @member
             * @type {LocalMusicFile[]}
             */
            this["data"] = [];
        }
        if (!("stats" in $$source)) {
            /**
             * @member
             * @type {LocalMusicStats}
             */
            this["stats"] = (new LocalMusicStats());
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new LocalMusicResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {LocalMusicResponse}
     */
    static createFrom($$source = {}) {
        const $$createField2_0 = $$createType12;
        const $$createField3_0 = $$createType13;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("data" in $$parsedSource) {
            $$parsedSource["data"] = $$createField2_0($$parsedSource["data"]);
        }
        if ("stats" in $$parsedSource) {
            $$parsedSource["stats"] = $$createField3_0($$parsedSource["stats"]);
        }
        return new LocalMusicResponse(/** @type {Partial<LocalMusicResponse>} */($$parsedSource));
    }
}

/**
 * LocalMusicStats 本地音乐统计信息
 */
export class LocalMusicStats {
    /**
     * Creates a new LocalMusicStats instance.
     * @param {Partial<LocalMusicStats>} [$$source = {}] - The source object to create the LocalMusicStats.
     */
    constructor($$source = {}) {
        if (!("total_songs" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total_songs"] = 0;
        }
        if (!("total_author_names" in $$source)) {
            /**
             * 与前端字段名保持一致
             * @member
             * @type {number}
             */
            this["total_author_names"] = 0;
        }
        if (!("total_albums" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total_albums"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new LocalMusicStats instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {LocalMusicStats}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new LocalMusicStats(/** @type {Partial<LocalMusicStats>} */($$parsedSource));
    }
}

/**
 * LoginData 登录成功时的数据结构
 */
export class LoginData {
    /**
     * Creates a new LoginData instance.
     * @param {Partial<LoginData>} [$$source = {}] - The source object to create the LoginData.
     */
    constructor($$source = {}) {
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {string | undefined}
             */
            this["token"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {number | undefined}
             */
            this["userid"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {any | undefined}
             */
            this["user_info"] = undefined;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new LoginData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {LoginData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new LoginData(/** @type {Partial<LoginData>} */($$parsedSource));
    }
}

/**
 * LoginResponse 登录响应结构 (保持向后兼容)
 */
export const LoginResponse = ApiResponse;

/**
 * LoginResponse 登录响应结构 (保持向后兼容)
 * @typedef {ApiResponse<LoginData>} LoginResponse
 */

/**
 * NewAlbumCategoryResponse 新碟上架分类响应结构
 */
export class NewAlbumCategoryResponse {
    /**
     * Creates a new NewAlbumCategoryResponse instance.
     * @param {Partial<NewAlbumCategoryResponse>} [$$source = {}] - The source object to create the NewAlbumCategoryResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }
        if (!("error_code" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["error_code"] = 0;
        }
        if (!("status" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["status"] = 0;
        }
        if (!("data" in $$source)) {
            /**
             * @member
             * @type {{ [_: string]: NewAlbumData[] }}
             */
            this["data"] = {};
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new NewAlbumCategoryResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {NewAlbumCategoryResponse}
     */
    static createFrom($$source = {}) {
        const $$createField4_0 = $$createType16;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("data" in $$parsedSource) {
            $$parsedSource["data"] = $$createField4_0($$parsedSource["data"]);
        }
        return new NewAlbumCategoryResponse(/** @type {Partial<NewAlbumCategoryResponse>} */($$parsedSource));
    }
}

/**
 * NewAlbumData 新碟上架数据结构
 */
export class NewAlbumData {
    /**
     * Creates a new NewAlbumData instance.
     * @param {Partial<NewAlbumData>} [$$source = {}] - The source object to create the NewAlbumData.
     */
    constructor($$source = {}) {
        if (!("id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["id"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("release_date" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["release_date"] = "";
        }
        if (!("song_count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["song_count"] = 0;
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("description" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["description"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new NewAlbumData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {NewAlbumData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new NewAlbumData(/** @type {Partial<NewAlbumData>} */($$parsedSource));
    }
}

/**
 * NewAlbumResponse 新碟上架响应结构
 */
export const NewAlbumResponse = ApiResponse;

/**
 * NewAlbumResponse 新碟上架响应结构
 * @typedef {ApiResponse<NewAlbumData[]>} NewAlbumResponse
 */

/**
 * NewSongData 新歌速递数据结构
 */
export class NewSongData {
    /**
     * Creates a new NewSongData instance.
     * @param {Partial<NewSongData>} [$$source = {}] - The source object to create the NewSongData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new NewSongData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {NewSongData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new NewSongData(/** @type {Partial<NewSongData>} */($$parsedSource));
    }
}

/**
 * NewSongResponse 新歌速递响应结构
 */
export const NewSongResponse = ApiResponse;

/**
 * NewSongResponse 新歌速递响应结构
 * @typedef {ApiResponse<NewSongData[]>} NewSongResponse
 */

/**
 * OSDLyricsResponse OSD歌词响应结构
 */
export class OSDLyricsResponse {
    /**
     * Creates a new OSDLyricsResponse instance.
     * @param {Partial<OSDLyricsResponse>} [$$source = {}] - The source object to create the OSDLyricsResponse.
     */
    constructor($$source = {}) {
        if (!("success" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["success"] = false;
        }
        if (!("message" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["message"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new OSDLyricsResponse instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {OSDLyricsResponse}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new OSDLyricsResponse(/** @type {Partial<OSDLyricsResponse>} */($$parsedSource));
    }
}

/**
 * PlayHistoryData 播放历史数据结构
 */
export class PlayHistoryData {
    /**
     * Creates a new PlayHistoryData instance.
     * @param {Partial<PlayHistoryData>} [$$source = {}] - The source object to create the PlayHistoryData.
     */
    constructor($$source = {}) {
        if (!("records" in $$source)) {
            /**
             * 播放记录列表
             * @member
             * @type {PlayHistoryRecord[]}
             */
            this["records"] = [];
        }
        if (!("total_count" in $$source)) {
            /**
             * 总记录数
             * @member
             * @type {number}
             */
            this["total_count"] = 0;
        }
        if (!("update_time" in $$source)) {
            /**
             * 更新时间
             * @member
             * @type {time$0.Time}
             */
            this["update_time"] = null;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PlayHistoryData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PlayHistoryData}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType18;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("records" in $$parsedSource) {
            $$parsedSource["records"] = $$createField0_0($$parsedSource["records"]);
        }
        return new PlayHistoryData(/** @type {Partial<PlayHistoryData>} */($$parsedSource));
    }
}

/**
 * PlayHistoryRecord 播放历史记录
 */
export class PlayHistoryRecord {
    /**
     * Creates a new PlayHistoryRecord instance.
     * @param {Partial<PlayHistoryRecord>} [$$source = {}] - The source object to create the PlayHistoryRecord.
     */
    constructor($$source = {}) {
        if (!("id" in $$source)) {
            /**
             * 记录ID（使用歌曲hash）
             * @member
             * @type {string}
             */
            this["id"] = "";
        }
        if (!("hash" in $$source)) {
            /**
             * 歌曲hash
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * 歌曲名称
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * 文件名
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * 艺术家名称
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * 专辑名称
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * 专辑ID
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * 歌曲时长（秒）
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("union_cover" in $$source)) {
            /**
             * 封面图片
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("play_time" in $$source)) {
            /**
             * 播放时间
             * @member
             * @type {time$0.Time}
             */
            this["play_time"] = null;
        }
        if (!("play_count" in $$source)) {
            /**
             * 播放次数
             * @member
             * @type {number}
             */
            this["play_count"] = 0;
        }
        if (!("last_play_time" in $$source)) {
            /**
             * 最后播放时间
             * @member
             * @type {time$0.Time}
             */
            this["last_play_time"] = null;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PlayHistoryRecord instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PlayHistoryRecord}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new PlayHistoryRecord(/** @type {Partial<PlayHistoryRecord>} */($$parsedSource));
    }
}

/**
 * PlayHistoryResponse 播放历史响应结构
 */
export const PlayHistoryResponse = ApiResponse;

/**
 * PlayHistoryResponse 播放历史响应结构
 * @typedef {ApiResponse<PlayHistoryData>} PlayHistoryResponse
 */

/**
 * PlaybackSettings 播放设置
 */
export class PlaybackSettings {
    /**
     * Creates a new PlaybackSettings instance.
     * @param {Partial<PlaybackSettings>} [$$source = {}] - The source object to create the PlaybackSettings.
     */
    constructor($$source = {}) {
        if (!("autoPlay" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["autoPlay"] = false;
        }
        if (!("crossfade" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["crossfade"] = false;
        }
        if (!("crossfadeDuration" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["crossfadeDuration"] = 0;
        }
        if (!("gaplessPlayback" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["gaplessPlayback"] = false;
        }
        if (!("replayGain" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["replayGain"] = false;
        }
        if (!("volume" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["volume"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PlaybackSettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PlaybackSettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new PlaybackSettings(/** @type {Partial<PlaybackSettings>} */($$parsedSource));
    }
}

/**
 * PlayerPlaylistData 播放器播放列表数据结构
 */
export class PlayerPlaylistData {
    /**
     * Creates a new PlayerPlaylistData instance.
     * @param {Partial<PlayerPlaylistData>} [$$source = {}] - The source object to create the PlayerPlaylistData.
     */
    constructor($$source = {}) {
        if (!("songs" in $$source)) {
            /**
             * 歌曲列表
             * @member
             * @type {PlayerPlaylistSong[]}
             */
            this["songs"] = [];
        }
        if (!("current_index" in $$source)) {
            /**
             * 当前播放索引
             * @member
             * @type {number}
             */
            this["current_index"] = 0;
        }
        if (!("play_mode" in $$source)) {
            /**
             * 播放模式：normal, shuffle, repeat_one, repeat_all
             * @member
             * @type {string}
             */
            this["play_mode"] = "";
        }
        if (!("shuffle_mode" in $$source)) {
            /**
             * 随机播放模式
             * @member
             * @type {boolean}
             */
            this["shuffle_mode"] = false;
        }
        if (!("repeat_mode" in $$source)) {
            /**
             * 循环模式：off, one, all
             * @member
             * @type {string}
             */
            this["repeat_mode"] = "";
        }
        if (!("name" in $$source)) {
            /**
             * 播放列表名称
             * @member
             * @type {string}
             */
            this["name"] = "";
        }
        if (!("update_time" in $$source)) {
            /**
             * 更新时间
             * @member
             * @type {time$0.Time}
             */
            this["update_time"] = null;
        }
        if (!("shuffle_order" in $$source)) {
            /**
             * 随机播放顺序
             * @member
             * @type {number[]}
             */
            this["shuffle_order"] = [];
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PlayerPlaylistData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PlayerPlaylistData}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType19;
        const $$createField7_0 = $$createType20;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("songs" in $$parsedSource) {
            $$parsedSource["songs"] = $$createField0_0($$parsedSource["songs"]);
        }
        if ("shuffle_order" in $$parsedSource) {
            $$parsedSource["shuffle_order"] = $$createField7_0($$parsedSource["shuffle_order"]);
        }
        return new PlayerPlaylistData(/** @type {Partial<PlayerPlaylistData>} */($$parsedSource));
    }
}

/**
 * PlayerPlaylistResponse 播放列表响应结构
 */
export const PlayerPlaylistResponse = ApiResponse;

/**
 * PlayerPlaylistResponse 播放列表响应结构
 * @typedef {ApiResponse<PlayerPlaylistData>} PlayerPlaylistResponse
 */

/**
 * PlayerPlaylistSong 播放列表中的歌曲
 */
export class PlayerPlaylistSong {
    /**
     * Creates a new PlayerPlaylistSong instance.
     * @param {Partial<PlayerPlaylistSong>} [$$source = {}] - The source object to create the PlayerPlaylistSong.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * 歌曲hash
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * 歌曲名称
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * 文件名
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * 艺术家名称
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * 专辑名称
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * 专辑ID
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * 歌曲时长（秒）
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("union_cover" in $$source)) {
            /**
             * 封面图片
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PlayerPlaylistSong instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PlayerPlaylistSong}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new PlayerPlaylistSong(/** @type {Partial<PlayerPlaylistSong>} */($$parsedSource));
    }
}

/**
 * PlaylistData 歌单数据结构
 */
export class PlaylistData {
    /**
     * Creates a new PlaylistData instance.
     * @param {Partial<PlaylistData>} [$$source = {}] - The source object to create the PlaylistData.
     */
    constructor($$source = {}) {
        if (!("global_collection_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["global_collection_id"] = "";
        }
        if (!("listid" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["listid"] = 0;
        }
        if (!("name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["name"] = "";
        }
        if (!("intro" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["intro"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }
        if (!("count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["count"] = 0;
        }
        if (!("type" in $$source)) {
            /**
             * 0-我创建的，1-我收藏的
             * @member
             * @type {number}
             */
            this["type"] = 0;
        }
        if (!("create_time" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["create_time"] = 0;
        }
        if (!("update_time" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["update_time"] = 0;
        }
        if (!("create_user_pic" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["create_user_pic"] = "";
        }
        if (!("create_username" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["create_username"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PlaylistData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PlaylistData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new PlaylistData(/** @type {Partial<PlaylistData>} */($$parsedSource));
    }
}

/**
 * PlaylistResponse 歌单响应结构
 */
export const PlaylistResponse = ApiResponse;

/**
 * PlaylistResponse 歌单响应结构
 * @typedef {ApiResponse<PlaylistData[]>} PlaylistResponse
 */

/**
 * PrivacySettings 隐私设置
 */
export class PrivacySettings {
    /**
     * Creates a new PrivacySettings instance.
     * @param {Partial<PrivacySettings>} [$$source = {}] - The source object to create the PrivacySettings.
     */
    constructor($$source = {}) {
        if (!("saveHistory" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["saveHistory"] = false;
        }
        if (!("shareListening" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["shareListening"] = false;
        }
        if (!("analytics" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["analytics"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new PrivacySettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {PrivacySettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new PrivacySettings(/** @type {Partial<PrivacySettings>} */($$parsedSource));
    }
}

/**
 * QRCodeData 二维码生成响应的数据结构
 */
export class QRCodeData {
    /**
     * Creates a new QRCodeData instance.
     * @param {Partial<QRCodeData>} [$$source = {}] - The source object to create the QRCodeData.
     */
    constructor($$source = {}) {
        if (!("url" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["url"] = "";
        }
        if (!("base64" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["base64"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new QRCodeData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {QRCodeData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new QRCodeData(/** @type {Partial<QRCodeData>} */($$parsedSource));
    }
}

/**
 * QRCodeResponse 二维码生成响应结构
 */
export const QRCodeResponse = ApiResponse;

/**
 * QRCodeResponse 二维码生成响应结构
 * @typedef {ApiResponse<QRCodeData>} QRCodeResponse
 */

/**
 * QRKeyData 二维码Key响应的数据结构
 */
export class QRKeyData {
    /**
     * Creates a new QRKeyData instance.
     * @param {Partial<QRKeyData>} [$$source = {}] - The source object to create the QRKeyData.
     */
    constructor($$source = {}) {
        if (!("qrcode" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["qrcode"] = "";
        }
        if (!("qrcode_img" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["qrcode_img"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new QRKeyData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {QRKeyData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new QRKeyData(/** @type {Partial<QRKeyData>} */($$parsedSource));
    }
}

/**
 * QRKeyResponse 二维码Key响应结构
 */
export const QRKeyResponse = ApiResponse;

/**
 * QRKeyResponse 二维码Key响应结构
 * @typedef {ApiResponse<QRKeyData>} QRKeyResponse
 */

/**
 * QRStatusData 二维码状态检测响应的数据结构
 */
export class QRStatusData {
    /**
     * Creates a new QRStatusData instance.
     * @param {Partial<QRStatusData>} [$$source = {}] - The source object to create the QRStatusData.
     */
    constructor($$source = {}) {
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {string | undefined}
             */
            this["nickname"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {string | undefined}
             */
            this["pic"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {string | undefined}
             */
            this["token"] = undefined;
        }
        if (/** @type {any} */(false)) {
            /**
             * @member
             * @type {number | undefined}
             */
            this["userid"] = undefined;
        }
        if (!("status" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["status"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new QRStatusData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {QRStatusData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new QRStatusData(/** @type {Partial<QRStatusData>} */($$parsedSource));
    }
}

/**
 * QRStatusResponse 二维码状态检测响应结构
 */
export const QRStatusResponse = ApiResponse;

/**
 * QRStatusResponse 二维码状态检测响应结构
 * @typedef {ApiResponse<QRStatusData>} QRStatusResponse
 */

/**
 * QualitySettings 音质设置
 */
export class QualitySettings {
    /**
     * Creates a new QualitySettings instance.
     * @param {Partial<QualitySettings>} [$$source = {}] - The source object to create the QualitySettings.
     */
    constructor($$source = {}) {
        if (!("streamingQuality" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["streamingQuality"] = "";
        }
        if (!("downloadQuality" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["downloadQuality"] = "";
        }
        if (!("wifiOnly" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["wifiOnly"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new QualitySettings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {QualitySettings}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new QualitySettings(/** @type {Partial<QualitySettings>} */($$parsedSource));
    }
}

/**
 * RecommendSongData 推荐歌曲数据结构
 */
export class RecommendSongData {
    /**
     * Creates a new RecommendSongData instance.
     * @param {Partial<RecommendSongData>} [$$source = {}] - The source object to create the RecommendSongData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new RecommendSongData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {RecommendSongData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new RecommendSongData(/** @type {Partial<RecommendSongData>} */($$parsedSource));
    }
}

/**
 * RecommendSongResponse 推荐歌曲响应结构
 */
export const RecommendSongResponse = ApiResponse;

/**
 * RecommendSongResponse 推荐歌曲响应结构
 * @typedef {ApiResponse<RecommendSongData[]>} RecommendSongResponse
 */

export class ResAlbum {
    /**
     * Creates a new ResAlbum instance.
     * @param {Partial<ResAlbum>} [$$source = {}] - The source object to create the ResAlbum.
     */
    constructor($$source = {}) {
        if (!("list" in $$source)) {
            /**
             * @member
             * @type {SearchAlbumData[]}
             */
            this["list"] = [];
        }
        if (!("total" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ResAlbum instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {ResAlbum}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType22;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("list" in $$parsedSource) {
            $$parsedSource["list"] = $$createField0_0($$parsedSource["list"]);
        }
        return new ResAlbum(/** @type {Partial<ResAlbum>} */($$parsedSource));
    }
}

export class ResArtist {
    /**
     * Creates a new ResArtist instance.
     * @param {Partial<ResArtist>} [$$source = {}] - The source object to create the ResArtist.
     */
    constructor($$source = {}) {
        if (!("list" in $$source)) {
            /**
             * @member
             * @type {SearchArtistData[]}
             */
            this["list"] = [];
        }
        if (!("total" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ResArtist instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {ResArtist}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType24;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("list" in $$parsedSource) {
            $$parsedSource["list"] = $$createField0_0($$parsedSource["list"]);
        }
        return new ResArtist(/** @type {Partial<ResArtist>} */($$parsedSource));
    }
}

export class ResMV {
    /**
     * Creates a new ResMV instance.
     * @param {Partial<ResMV>} [$$source = {}] - The source object to create the ResMV.
     */
    constructor($$source = {}) {
        if (!("list" in $$source)) {
            /**
             * @member
             * @type {SearchMVData[]}
             */
            this["list"] = [];
        }
        if (!("total" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ResMV instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {ResMV}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType26;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("list" in $$parsedSource) {
            $$parsedSource["list"] = $$createField0_0($$parsedSource["list"]);
        }
        return new ResMV(/** @type {Partial<ResMV>} */($$parsedSource));
    }
}

export class ResPlaylist {
    /**
     * Creates a new ResPlaylist instance.
     * @param {Partial<ResPlaylist>} [$$source = {}] - The source object to create the ResPlaylist.
     */
    constructor($$source = {}) {
        if (!("list" in $$source)) {
            /**
             * @member
             * @type {SearchPlaylistData[]}
             */
            this["list"] = [];
        }
        if (!("total" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ResPlaylist instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {ResPlaylist}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType28;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("list" in $$parsedSource) {
            $$parsedSource["list"] = $$createField0_0($$parsedSource["list"]);
        }
        return new ResPlaylist(/** @type {Partial<ResPlaylist>} */($$parsedSource));
    }
}

export class ResSong {
    /**
     * Creates a new ResSong instance.
     * @param {Partial<ResSong>} [$$source = {}] - The source object to create the ResSong.
     */
    constructor($$source = {}) {
        if (!("list" in $$source)) {
            /**
             * @member
             * @type {SearchSongData[]}
             */
            this["list"] = [];
        }
        if (!("total" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["total"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ResSong instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {ResSong}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType30;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("list" in $$parsedSource) {
            $$parsedSource["list"] = $$createField0_0($$parsedSource["list"]);
        }
        return new ResSong(/** @type {Partial<ResSong>} */($$parsedSource));
    }
}

/**
 * SearchAlbumData 搜索专辑数据结构
 */
export class SearchAlbumData {
    /**
     * Creates a new SearchAlbumData instance.
     * @param {Partial<SearchAlbumData>} [$$source = {}] - The source object to create the SearchAlbumData.
     */
    constructor($$source = {}) {
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("img_url" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["img_url"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("song_count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["song_count"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchAlbumData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchAlbumData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SearchAlbumData(/** @type {Partial<SearchAlbumData>} */($$parsedSource));
    }
}

/**
 * SearchArtistData 搜索艺人数据结构
 */
export class SearchArtistData {
    /**
     * Creates a new SearchArtistData instance.
     * @param {Partial<SearchArtistData>} [$$source = {}] - The source object to create the SearchArtistData.
     */
    constructor($$source = {}) {
        if (!("author_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("avatar" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["avatar"] = "";
        }
        if (!("song_count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["song_count"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchArtistData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchArtistData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SearchArtistData(/** @type {Partial<SearchArtistData>} */($$parsedSource));
    }
}

/**
 * SearchMVData 搜索MV数据结构
 */
export class SearchMVData {
    /**
     * Creates a new SearchMVData instance.
     * @param {Partial<SearchMVData>} [$$source = {}] - The source object to create the SearchMVData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("mv_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["mv_name"] = "";
        }
        if (!("img_url" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["img_url"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchMVData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchMVData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SearchMVData(/** @type {Partial<SearchMVData>} */($$parsedSource));
    }
}

/**
 * SearchPlaylistData 搜索歌单数据结构
 */
export class SearchPlaylistData {
    /**
     * Creates a new SearchPlaylistData instance.
     * @param {Partial<SearchPlaylistData>} [$$source = {}] - The source object to create the SearchPlaylistData.
     */
    constructor($$source = {}) {
        if (!("special_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["special_id"] = "";
        }
        if (!("special_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["special_name"] = "";
        }
        if (!("img_url" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["img_url"] = "";
        }
        if (!("play_count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["play_count"] = 0;
        }
        if (!("song_count" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["song_count"] = 0;
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchPlaylistData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchPlaylistData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SearchPlaylistData(/** @type {Partial<SearchPlaylistData>} */($$parsedSource));
    }
}

/**
 * SearchResponse 搜索响应结构
 */
export const SearchResponse = ApiResponse;

/**
 * SearchResponse 搜索响应结构
 * @typedef {ApiResponse<SearchResults>} SearchResponse
 */

/**
 * SearchResults 搜索结果数据结构
 */
export class SearchResults {
    /**
     * Creates a new SearchResults instance.
     * @param {Partial<SearchResults>} [$$source = {}] - The source object to create the SearchResults.
     */
    constructor($$source = {}) {
        if (!("songs" in $$source)) {
            /**
             * @member
             * @type {ResSong}
             */
            this["songs"] = (new ResSong());
        }
        if (!("artists" in $$source)) {
            /**
             * @member
             * @type {ResArtist}
             */
            this["artists"] = (new ResArtist());
        }
        if (!("playlists" in $$source)) {
            /**
             * @member
             * @type {ResPlaylist}
             */
            this["playlists"] = (new ResPlaylist());
        }
        if (!("albums" in $$source)) {
            /**
             * @member
             * @type {ResAlbum}
             */
            this["albums"] = (new ResAlbum());
        }
        if (!("mvs" in $$source)) {
            /**
             * 总数量
             * @member
             * @type {ResMV}
             */
            this["mvs"] = (new ResMV());
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchResults instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchResults}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType31;
        const $$createField1_0 = $$createType32;
        const $$createField2_0 = $$createType33;
        const $$createField3_0 = $$createType34;
        const $$createField4_0 = $$createType35;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("songs" in $$parsedSource) {
            $$parsedSource["songs"] = $$createField0_0($$parsedSource["songs"]);
        }
        if ("artists" in $$parsedSource) {
            $$parsedSource["artists"] = $$createField1_0($$parsedSource["artists"]);
        }
        if ("playlists" in $$parsedSource) {
            $$parsedSource["playlists"] = $$createField2_0($$parsedSource["playlists"]);
        }
        if ("albums" in $$parsedSource) {
            $$parsedSource["albums"] = $$createField3_0($$parsedSource["albums"]);
        }
        if ("mvs" in $$parsedSource) {
            $$parsedSource["mvs"] = $$createField4_0($$parsedSource["mvs"]);
        }
        return new SearchResults(/** @type {Partial<SearchResults>} */($$parsedSource));
    }
}

/**
 * SearchSongData 搜索歌曲数据结构
 */
export class SearchSongData {
    /**
     * Creates a new SearchSongData instance.
     * @param {Partial<SearchSongData>} [$$source = {}] - The source object to create the SearchSongData.
     */
    constructor($$source = {}) {
        if (!("hash" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["hash"] = "";
        }
        if (!("songname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["songname"] = "";
        }
        if (!("filename" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["filename"] = "";
        }
        if (!("time_length" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["time_length"] = 0;
        }
        if (!("album_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_name"] = "";
        }
        if (!("album_id" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["album_id"] = "";
        }
        if (!("author_name" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["author_name"] = "";
        }
        if (!("union_cover" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["union_cover"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchSongData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchSongData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SearchSongData(/** @type {Partial<SearchSongData>} */($$parsedSource));
    }
}

/**
 * SearchSuggestData 搜索建议数据结构
 */
export class SearchSuggestData {
    /**
     * Creates a new SearchSuggestData instance.
     * @param {Partial<SearchSuggestData>} [$$source = {}] - The source object to create the SearchSuggestData.
     */
    constructor($$source = {}) {
        if (!("keyword" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["keyword"] = "";
        }
        if (!("type" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["type"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SearchSuggestData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SearchSuggestData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SearchSuggestData(/** @type {Partial<SearchSuggestData>} */($$parsedSource));
    }
}

/**
 * SearchSuggestResponse 搜索建议响应结构
 */
export const SearchSuggestResponse = ApiResponse;

/**
 * SearchSuggestResponse 搜索建议响应结构
 * @typedef {ApiResponse<SearchSuggestData[]>} SearchSuggestResponse
 */

/**
 * SetPlaylistRequest 设置播放列表请求
 */
export class SetPlaylistRequest {
    /**
     * Creates a new SetPlaylistRequest instance.
     * @param {Partial<SetPlaylistRequest>} [$$source = {}] - The source object to create the SetPlaylistRequest.
     */
    constructor($$source = {}) {
        if (!("songs" in $$source)) {
            /**
             * 歌曲列表
             * @member
             * @type {PlayerPlaylistSong[]}
             */
            this["songs"] = [];
        }
        if (!("current_index" in $$source)) {
            /**
             * 当前播放索引
             * @member
             * @type {number}
             */
            this["current_index"] = 0;
        }
        if (!("name" in $$source)) {
            /**
             * 播放列表名称
             * @member
             * @type {string}
             */
            this["name"] = "";
        }
        if (!("play_mode" in $$source)) {
            /**
             * 播放模式
             * @member
             * @type {string}
             */
            this["play_mode"] = "";
        }
        if (!("clear_first" in $$source)) {
            /**
             * 是否先清空现有列表
             * @member
             * @type {boolean}
             */
            this["clear_first"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SetPlaylistRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SetPlaylistRequest}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType19;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("songs" in $$parsedSource) {
            $$parsedSource["songs"] = $$createField0_0($$parsedSource["songs"]);
        }
        return new SetPlaylistRequest(/** @type {Partial<SetPlaylistRequest>} */($$parsedSource));
    }
}

/**
 * Settings 设置数据结构
 */
export class Settings {
    /**
     * Creates a new Settings instance.
     * @param {Partial<Settings>} [$$source = {}] - The source object to create the Settings.
     */
    constructor($$source = {}) {
        if (!("playback" in $$source)) {
            /**
             * 播放设置
             * @member
             * @type {PlaybackSettings}
             */
            this["playback"] = (new PlaybackSettings());
        }
        if (!("quality" in $$source)) {
            /**
             * 音质设置
             * @member
             * @type {QualitySettings}
             */
            this["quality"] = (new QualitySettings());
        }
        if (!("interface" in $$source)) {
            /**
             * 界面设置
             * @member
             * @type {InterfaceSettings}
             */
            this["interface"] = (new InterfaceSettings());
        }
        if (!("download" in $$source)) {
            /**
             * 下载设置
             * @member
             * @type {DownloadSettings}
             */
            this["download"] = (new DownloadSettings());
        }
        if (!("hotkeys" in $$source)) {
            /**
             * 快捷键设置
             * @member
             * @type {HotkeysSettings}
             */
            this["hotkeys"] = (new HotkeysSettings());
        }
        if (!("privacy" in $$source)) {
            /**
             * 隐私设置
             * @member
             * @type {PrivacySettings}
             */
            this["privacy"] = (new PrivacySettings());
        }
        if (!("behavior" in $$source)) {
            /**
             * 应用行为设置
             * @member
             * @type {BehaviorSettings}
             */
            this["behavior"] = (new BehaviorSettings());
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Settings instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {Settings}
     */
    static createFrom($$source = {}) {
        const $$createField0_0 = $$createType36;
        const $$createField1_0 = $$createType37;
        const $$createField2_0 = $$createType38;
        const $$createField3_0 = $$createType39;
        const $$createField4_0 = $$createType40;
        const $$createField5_0 = $$createType41;
        const $$createField6_0 = $$createType42;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("playback" in $$parsedSource) {
            $$parsedSource["playback"] = $$createField0_0($$parsedSource["playback"]);
        }
        if ("quality" in $$parsedSource) {
            $$parsedSource["quality"] = $$createField1_0($$parsedSource["quality"]);
        }
        if ("interface" in $$parsedSource) {
            $$parsedSource["interface"] = $$createField2_0($$parsedSource["interface"]);
        }
        if ("download" in $$parsedSource) {
            $$parsedSource["download"] = $$createField3_0($$parsedSource["download"]);
        }
        if ("hotkeys" in $$parsedSource) {
            $$parsedSource["hotkeys"] = $$createField4_0($$parsedSource["hotkeys"]);
        }
        if ("privacy" in $$parsedSource) {
            $$parsedSource["privacy"] = $$createField5_0($$parsedSource["privacy"]);
        }
        if ("behavior" in $$parsedSource) {
            $$parsedSource["behavior"] = $$createField6_0($$parsedSource["behavior"]);
        }
        return new Settings(/** @type {Partial<Settings>} */($$parsedSource));
    }
}

/**
 * SongUrlData 歌曲播放地址数据结构
 */
export class SongUrlData {
    /**
     * Creates a new SongUrlData instance.
     * @param {Partial<SongUrlData>} [$$source = {}] - The source object to create the SongUrlData.
     */
    constructor($$source = {}) {
        if (!("url" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["url"] = "";
        }
        if (!("backupUrl" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["backupUrl"] = "";
        }
        if (!("lyrics" in $$source)) {
            /**
             * 歌词内容
             * @member
             * @type {string}
             */
            this["lyrics"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new SongUrlData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {SongUrlData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new SongUrlData(/** @type {Partial<SongUrlData>} */($$parsedSource));
    }
}

/**
 * SongUrlResponse 歌曲播放地址响应结构
 */
export const SongUrlResponse = ApiResponse;

/**
 * SongUrlResponse 歌曲播放地址响应结构
 * @typedef {ApiResponse<SongUrlData>} SongUrlResponse
 */

/**
 * UpdatePlayModeRequest 更新播放模式请求
 */
export class UpdatePlayModeRequest {
    /**
     * Creates a new UpdatePlayModeRequest instance.
     * @param {Partial<UpdatePlayModeRequest>} [$$source = {}] - The source object to create the UpdatePlayModeRequest.
     */
    constructor($$source = {}) {
        if (!("shuffle_mode" in $$source)) {
            /**
             * 随机播放模式
             * @member
             * @type {boolean}
             */
            this["shuffle_mode"] = false;
        }
        if (!("repeat_mode" in $$source)) {
            /**
             * 循环模式：off, one, all
             * @member
             * @type {string}
             */
            this["repeat_mode"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new UpdatePlayModeRequest instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {UpdatePlayModeRequest}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new UpdatePlayModeRequest(/** @type {Partial<UpdatePlayModeRequest>} */($$parsedSource));
    }
}

/**
 * UserDetailData 用户详情数据结构
 */
export class UserDetailData {
    /**
     * Creates a new UserDetailData instance.
     * @param {Partial<UserDetailData>} [$$source = {}] - The source object to create the UserDetailData.
     */
    constructor($$source = {}) {
        if (!("userid" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["userid"] = 0;
        }
        if (!("login_time" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["login_time"] = 0;
        }
        if (!("nickname" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["nickname"] = "";
        }
        if (!("pic" in $$source)) {
            /**
             * @member
             * @type {string}
             */
            this["pic"] = "";
        }
        if (!("vip_type" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["vip_type"] = 0;
        }
        if (!("vip_level" in $$source)) {
            /**
             * @member
             * @type {number}
             */
            this["vip_level"] = 0;
        }
        if (!("is_vip" in $$source)) {
            /**
             * @member
             * @type {boolean}
             */
            this["is_vip"] = false;
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new UserDetailData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {UserDetailData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new UserDetailData(/** @type {Partial<UserDetailData>} */($$parsedSource));
    }
}

/**
 * UserDetailResponse 用户详情响应结构
 */
export const UserDetailResponse = ApiResponse;

/**
 * UserDetailResponse 用户详情响应结构
 * @typedef {ApiResponse<UserDetailData>} UserDetailResponse
 */

/**
 * VipDetailData VIP详情数据结构
 */
export class VipDetailData {
    /**
     * Creates a new VipDetailData instance.
     * @param {Partial<VipDetailData>} [$$source = {}] - The source object to create the VipDetailData.
     */
    constructor($$source = {}) {
        if (!("is_vip" in $$source)) {
            /**
             * 1是VIP，0不是VIP
             * @member
             * @type {number}
             */
            this["is_vip"] = 0;
        }
        if (!("vip_end_time" in $$source)) {
            /**
             * VIP结束时间字符串
             * @member
             * @type {string}
             */
            this["vip_end_time"] = "";
        }
        if (!("product_type" in $$source)) {
            /**
             * VIP类型
             * @member
             * @type {string}
             */
            this["product_type"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new VipDetailData instance from a string or object.
     * @param {any} [$$source = {}]
     * @returns {VipDetailData}
     */
    static createFrom($$source = {}) {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new VipDetailData(/** @type {Partial<VipDetailData>} */($$parsedSource));
    }
}

/**
 * VipDetailResponse VIP详情响应结构
 */
export const VipDetailResponse = ApiResponse;

/**
 * VipDetailResponse VIP详情响应结构
 * @typedef {ApiResponse<VipDetailData>} VipDetailResponse
 */

// Private type creation functions
const $$createType0 = PlayerPlaylistSong.createFrom;
const $$createType1 = AlbumDetailData.createFrom;
const $$createType2 = AlbumSongData.createFrom;
const $$createType3 = $Create.Array($$createType2);
const $$createType4 = DownloadRecord.createFrom;
const $$createType5 = $Create.Array($$createType4);
const $$createType6 = $Create.Array($Create.Any);
const $$createType7 = HotSearchKeyword.createFrom;
const $$createType8 = $Create.Array($$createType7);
const $$createType9 = HotSearchCategory.createFrom;
const $$createType10 = $Create.Array($$createType9);
const $$createType11 = LocalMusicFile.createFrom;
const $$createType12 = $Create.Array($$createType11);
const $$createType13 = LocalMusicStats.createFrom;
const $$createType14 = NewAlbumData.createFrom;
const $$createType15 = $Create.Array($$createType14);
const $$createType16 = $Create.Map($Create.Any, $$createType15);
const $$createType17 = PlayHistoryRecord.createFrom;
const $$createType18 = $Create.Array($$createType17);
const $$createType19 = $Create.Array($$createType0);
const $$createType20 = $Create.Array($Create.Any);
const $$createType21 = SearchAlbumData.createFrom;
const $$createType22 = $Create.Array($$createType21);
const $$createType23 = SearchArtistData.createFrom;
const $$createType24 = $Create.Array($$createType23);
const $$createType25 = SearchMVData.createFrom;
const $$createType26 = $Create.Array($$createType25);
const $$createType27 = SearchPlaylistData.createFrom;
const $$createType28 = $Create.Array($$createType27);
const $$createType29 = SearchSongData.createFrom;
const $$createType30 = $Create.Array($$createType29);
const $$createType31 = ResSong.createFrom;
const $$createType32 = ResArtist.createFrom;
const $$createType33 = ResPlaylist.createFrom;
const $$createType34 = ResAlbum.createFrom;
const $$createType35 = ResMV.createFrom;
const $$createType36 = PlaybackSettings.createFrom;
const $$createType37 = QualitySettings.createFrom;
const $$createType38 = InterfaceSettings.createFrom;
const $$createType39 = DownloadSettings.createFrom;
const $$createType40 = HotkeysSettings.createFrom;
const $$createType41 = PrivacySettings.createFrom;
const $$createType42 = BehaviorSettings.createFrom;
