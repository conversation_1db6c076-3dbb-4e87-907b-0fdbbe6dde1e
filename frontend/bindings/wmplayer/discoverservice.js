// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * DiscoverService 处理发现页面相关的服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * GetNewAlbums 获取新碟上架
 * @returns {$CancellablePromise<$models.NewAlbumResponse>}
 */
export function GetNewAlbums() {
    return $Call.ByID(866484816).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType2($result);
    }));
}

/**
 * GetNewAlbumsByCategory 获取分类的新碟上架数据
 * @returns {$CancellablePromise<$models.NewAlbumCategoryResponse>}
 */
export function GetNewAlbumsByCategory() {
    return $Call.ByID(1493678089).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType3($result);
    }));
}

/**
 * GetNewSongs 获取新歌速递
 * @returns {$CancellablePromise<$models.NewSongResponse>}
 */
export function GetNewSongs() {
    return $Call.ByID(2939567122).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

/**
 * GetRecommendSongs 获取推荐歌曲
 * @param {string} category
 * @returns {$CancellablePromise<$models.RecommendSongResponse>}
 */
export function GetRecommendSongs(category) {
    return $Call.ByID(2802721614, category).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType9($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.NewAlbumData.createFrom;
const $$createType1 = $Create.Array($$createType0);
const $$createType2 = $models.ApiResponse.createFrom($$createType1);
const $$createType3 = $models.NewAlbumCategoryResponse.createFrom;
const $$createType4 = $models.NewSongData.createFrom;
const $$createType5 = $Create.Array($$createType4);
const $$createType6 = $models.ApiResponse.createFrom($$createType5);
const $$createType7 = $models.RecommendSongData.createFrom;
const $$createType8 = $Create.Array($$createType7);
const $$createType9 = $models.ApiResponse.createFrom($$createType8);
