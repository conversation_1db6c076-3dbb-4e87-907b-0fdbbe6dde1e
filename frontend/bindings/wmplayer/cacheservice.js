// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * CacheService 音频缓存服务（包含OSD歌词功能）
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * CacheAudioFile 缓存音频文件（供前端调用）
 * @param {string} songHash
 * @param {string[]} urls
 * @returns {$CancellablePromise<$models.CacheResponse>}
 */
export function CacheAudioFile(songHash, urls) {
    return $Call.ByID(2654436345, songHash, urls).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * GetCachedURL 获取缓存的本地URL
 * @param {string} songHash
 * @returns {$CancellablePromise<$models.CacheResponse>}
 */
export function GetCachedURL(songHash) {
    return $Call.ByID(614397800, songHash).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * IsEnabled 检查OSD歌词是否启用
 * @returns {$CancellablePromise<boolean>}
 */
export function IsEnabled() {
    return $Call.ByID(3435818864);
}

/**
 * RegisterLocalMusic 注册本地音乐hash到文件路径的映射（供前端调用）
 * @param {string} localHash
 * @param {string} filePath
 * @returns {$CancellablePromise<$models.CacheResponse>}
 */
export function RegisterLocalMusic(localHash, filePath) {
    return $Call.ByID(3089293112, localHash, filePath).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * SetEnabled 设置OSD歌词开关状态
 * @param {boolean} enabled
 * @returns {$CancellablePromise<$models.CacheResponse>}
 */
export function SetEnabled(enabled) {
    return $Call.ByID(1138446006, enabled).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * StartHTTPServer 启动本地HTTP文件服务器
 * @returns {$CancellablePromise<void>}
 */
export function StartHTTPServer() {
    return $Call.ByID(1297567162);
}

/**
 * StartHTTPServerWithO0SDLyrics 启动本地HTTP文件服务器并支持OSD歌词SSE
 * @returns {$CancellablePromise<void>}
 */
export function StartHTTPServerWithOSDLyrics() {
    return $Call.ByID(618129676);
}

/**
 * StopHTTPServer 停止HTTP服务器
 * @returns {$CancellablePromise<void>}
 */
export function StopHTTPServer() {
    return $Call.ByID(1655861396);
}

/**
 * UpdateCurrentLyrics 更新当前显示的歌词行
 * @param {string} lyricsText
 * @param {string} songName
 * @param {string} artist
 * @returns {$CancellablePromise<$models.OSDLyricsResponse>}
 */
export function UpdateCurrentLyrics(lyricsText, songName, artist) {
    return $Call.ByID(2444780395, lyricsText, songName, artist).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.CacheResponse.createFrom;
const $$createType1 = $models.OSDLyricsResponse.createFrom;
