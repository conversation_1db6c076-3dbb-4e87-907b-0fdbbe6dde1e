// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * SettingsService 设置服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * GetSettingsPath 获取设置文件路径（用于前端显示）
 * @returns {$CancellablePromise<$models.ApiResponse<string> | null>}
 */
export function GetSettingsPath() {
    return $Call.ByID(3680721862).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * LoadSettings 加载设置
 * @returns {$CancellablePromise<$models.ApiResponse<$models.Settings> | null>}
 */
export function LoadSettings() {
    return $Call.ByID(3298483949).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType4($result);
    }));
}

/**
 * SaveSettings 保存设置
 * @param {$models.Settings} settings
 * @returns {$CancellablePromise<$models.ApiResponse<boolean> | null>}
 */
export function SaveSettings(settings) {
    return $Call.ByID(2662658340, settings).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.ApiResponse.createFrom($Create.Any);
const $$createType1 = $Create.Nullable($$createType0);
const $$createType2 = $models.Settings.createFrom;
const $$createType3 = $models.ApiResponse.createFrom($$createType2);
const $$createType4 = $Create.Nullable($$createType3);
const $$createType5 = $models.ApiResponse.createFrom($Create.Any);
const $$createType6 = $Create.Nullable($$createType5);
