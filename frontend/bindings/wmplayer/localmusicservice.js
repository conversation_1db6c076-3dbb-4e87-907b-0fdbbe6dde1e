// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * LocalMusicService 本地音乐服务结构体
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * GetAudioFileData 获取音频文件的二进制数据（保留兼容性）
 * @param {string} filePath
 * @returns {$CancellablePromise<$models.AudioFileResponse>}
 */
export function GetAudioFileData(filePath) {
    return $Call.ByID(1650987291, filePath).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * GetCachedMusicFiles 获取缓存的音乐文件
 * @returns {$CancellablePromise<$models.LocalMusicResponse>}
 */
export function GetCachedMusicFiles() {
    return $Call.ByID(4277727669).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * GetLocalAudioURL 获取本地音频文件的缓存URL
 * @param {string} filePath
 * @returns {$CancellablePromise<$models.CacheResponse>}
 */
export function GetLocalAudioURL(filePath) {
    return $Call.ByID(1962042063, filePath).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType2($result);
    }));
}

/**
 * GetLocalMusicLyrics 获取本地音乐文件的歌词
 * @param {string} filePath
 * @returns {$CancellablePromise<$models.CacheResponse>}
 */
export function GetLocalMusicLyrics(filePath) {
    return $Call.ByID(3932332491, filePath).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType2($result);
    }));
}

/**
 * ScanMusicFolder 扫描音乐文件夹
 * @param {string} folderPath
 * @returns {$CancellablePromise<$models.LocalMusicResponse>}
 */
export function ScanMusicFolder(folderPath) {
    return $Call.ByID(1436466749, folderPath).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * ScanMusicFolders 扫描多个音乐文件夹
 * @param {string[]} folderPaths
 * @returns {$CancellablePromise<$models.LocalMusicResponse>}
 */
export function ScanMusicFolders(folderPaths) {
    return $Call.ByID(384144586, folderPaths).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * SelectMusicFolder 选择音乐文件夹
 * @returns {$CancellablePromise<$models.FolderSelectResponse>}
 */
export function SelectMusicFolder() {
    return $Call.ByID(4207803074).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType3($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.AudioFileResponse.createFrom;
const $$createType1 = $models.LocalMusicResponse.createFrom;
const $$createType2 = $models.CacheResponse.createFrom;
const $$createType3 = $models.FolderSelectResponse.createFrom;
