// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * FavoritesService 处理我喜欢的页面相关的服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * AddFavorite 添加歌曲到我喜欢的
 * @param {$models.AddFavoriteRequest} request
 * @returns {$CancellablePromise<$models.AddFavoriteResponse>}
 */
export function AddFavorite(request) {
    return $Call.ByID(1345301207, request).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * GetFavoritesSongs 获取我喜欢的歌曲
 * @param {number} page
 * @param {number} pageSize
 * @returns {$CancellablePromise<$models.FavoritesSongResponse>}
 */
export function GetFavoritesSongs(page, pageSize) {
    return $Call.ByID(3294162645, page, pageSize).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType3($result);
    }));
}

/**
 * GetPlaylistSongs 获取歌单歌曲列表
 * @param {string} globalCollectionID
 * @returns {$CancellablePromise<$models.FavoritesSongResponse>}
 */
export function GetPlaylistSongs(globalCollectionID) {
    return $Call.ByID(604937116, globalCollectionID).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType3($result);
    }));
}

/**
 * GetUserPlaylists 获取用户歌单
 * @returns {$CancellablePromise<$models.PlaylistResponse>}
 */
export function GetUserPlaylists() {
    return $Call.ByID(672383946).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType6($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.AddFavoriteResponse.createFrom;
const $$createType1 = $models.FavoritesSongData.createFrom;
const $$createType2 = $Create.Array($$createType1);
const $$createType3 = $models.ApiResponse.createFrom($$createType2);
const $$createType4 = $models.PlaylistData.createFrom;
const $$createType5 = $Create.Array($$createType4);
const $$createType6 = $models.ApiResponse.createFrom($$createType5);
