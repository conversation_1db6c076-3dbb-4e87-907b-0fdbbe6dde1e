// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * HomepageService 处理首页相关的服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * GetAIRecommend 获取AI推荐歌曲
 * @returns {$CancellablePromise<$models.AIRecommendResponse>}
 */
export function GetAIRecommend() {
    return $Call.ByID(3757806269).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType2($result);
    }));
}

/**
 * GetDailyRecommend 获取每日推荐歌曲
 * @param {string} platform
 * @returns {$CancellablePromise<$models.DailyRecommendResponse>}
 */
export function GetDailyRecommend(platform) {
    return $Call.ByID(1340634890, platform).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType5($result);
    }));
}

/**
 * GetPersonalFM 获取私人FM歌曲
 * @param {$models.FmRequestParams} params
 * @returns {$CancellablePromise<$models.FmResponse>}
 */
export function GetPersonalFM(params) {
    return $Call.ByID(2699209526, params).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType8($result);
    }));
}

/**
 * GetPersonalFMAdvanced 获取私人FM歌曲（高级参数版本）
 * @param {string} hash
 * @param {string} songID
 * @param {number} playTime
 * @param {string} mode
 * @param {number} songPoolID
 * @param {boolean} isOverplay
 * @param {number} remainSongCnt
 * @returns {$CancellablePromise<$models.FmResponse>}
 */
export function GetPersonalFMAdvanced(hash, songID, playTime, mode, songPoolID, isOverplay, remainSongCnt) {
    return $Call.ByID(2993387204, hash, songID, playTime, mode, songPoolID, isOverplay, remainSongCnt).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType8($result);
    }));
}

/**
 * GetPersonalFMSimple 获取私人FM歌曲（简化版本）
 * @param {string} mode
 * @returns {$CancellablePromise<$models.FmResponse>}
 */
export function GetPersonalFMSimple(mode) {
    return $Call.ByID(1359166284, mode).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType8($result);
    }));
}

/**
 * GetPersonalFMWithParams 获取私人FM歌曲（带完整参数）
 * @param {string} mode
 * @param {number} songPoolID
 * @returns {$CancellablePromise<$models.FmResponse>}
 */
export function GetPersonalFMWithParams(mode, songPoolID) {
    return $Call.ByID(2432642640, mode, songPoolID).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType8($result);
    }));
}

/**
 * GetSongUrl 获取歌曲播放地址
 * @param {string} hash
 * @returns {$CancellablePromise<$models.SongUrlResponse>}
 */
export function GetSongUrl(hash) {
    return $Call.ByID(604469427, hash).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType10($result);
    }));
}

/**
 * ReportFMAction 报告私人FM操作（喜欢/不喜欢）
 * @param {string} hash
 * @param {string} songID
 * @param {string} action
 * @param {number} playTime
 * @returns {$CancellablePromise<$models.FmResponse>}
 */
export function ReportFMAction(hash, songID, action, playTime) {
    return $Call.ByID(1528444736, hash, songID, action, playTime).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType8($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.AIRecommendData.createFrom;
const $$createType1 = $Create.Array($$createType0);
const $$createType2 = $models.ApiResponse.createFrom($$createType1);
const $$createType3 = $models.DailyRecommendData.createFrom;
const $$createType4 = $Create.Array($$createType3);
const $$createType5 = $models.ApiResponse.createFrom($$createType4);
const $$createType6 = $models.FmSongData.createFrom;
const $$createType7 = $Create.Array($$createType6);
const $$createType8 = $models.ApiResponse.createFrom($$createType7);
const $$createType9 = $models.SongUrlData.createFrom;
const $$createType10 = $models.ApiResponse.createFrom($$createType9);
