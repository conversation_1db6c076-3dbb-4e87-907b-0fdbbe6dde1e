// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * LoginService 处理登录相关的服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as $models from "./models.js";

/**
 * CheckLoginStatus 检查登录状态
 * @returns {$CancellablePromise<$models.LoginResponse>}
 */
export function CheckLoginStatus() {
    return $Call.ByID(633082763).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * CheckQRStatus 检测二维码扫码状态
 * @param {string} key
 * @returns {$CancellablePromise<$models.QRStatusResponse>}
 */
export function CheckQRStatus(key) {
    return $Call.ByID(2149837409, key).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType3($result);
    }));
}

/**
 * ClaimDailyVip 领取每日VIP
 * @returns {$CancellablePromise<$models.LoginResponse>}
 */
export function ClaimDailyVip() {
    return $Call.ByID(806439244).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * CreateQRCode 根据Key生成二维码
 * @param {string} key
 * @returns {$CancellablePromise<$models.QRCodeResponse>}
 */
export function CreateQRCode(key) {
    return $Call.ByID(3694268466, key).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType5($result);
    }));
}

/**
 * GenerateQRKey 生成二维码登录Key
 * @returns {$CancellablePromise<$models.QRKeyResponse>}
 */
export function GenerateQRKey() {
    return $Call.ByID(3171259475).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType7($result);
    }));
}

/**
 * GetUserDetail 获取用户详情
 * @returns {$CancellablePromise<$models.UserDetailResponse>}
 */
export function GetUserDetail() {
    return $Call.ByID(1114743738).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType9($result);
    }));
}

/**
 * GetVipDetail 获取VIP详情
 * @returns {$CancellablePromise<$models.VipDetailResponse>}
 */
export function GetVipDetail() {
    return $Call.ByID(2446899626).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType11($result);
    }));
}

/**
 * LoginWithPhone 手机号登录
 * @param {string} mobile
 * @param {string} code
 * @returns {$CancellablePromise<$models.LoginResponse>}
 */
export function LoginWithPhone(mobile, code) {
    return $Call.ByID(1580188767, mobile, code).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * Logout 登出功能，清除Cookie
 * @returns {$CancellablePromise<$models.LoginResponse>}
 */
export function Logout() {
    return $Call.ByID(2373873122).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType1($result);
    }));
}

/**
 * SendCaptcha 发送验证码
 * @param {string} mobile
 * @returns {$CancellablePromise<$models.CaptchaResponse>}
 */
export function SendCaptcha(mobile) {
    return $Call.ByID(3021409104, mobile).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType13($result);
    }));
}

// Private type creation functions
const $$createType0 = $models.LoginData.createFrom;
const $$createType1 = $models.ApiResponse.createFrom($$createType0);
const $$createType2 = $models.QRStatusData.createFrom;
const $$createType3 = $models.ApiResponse.createFrom($$createType2);
const $$createType4 = $models.QRCodeData.createFrom;
const $$createType5 = $models.ApiResponse.createFrom($$createType4);
const $$createType6 = $models.QRKeyData.createFrom;
const $$createType7 = $models.ApiResponse.createFrom($$createType6);
const $$createType8 = $models.UserDetailData.createFrom;
const $$createType9 = $models.ApiResponse.createFrom($$createType8);
const $$createType10 = $models.VipDetailData.createFrom;
const $$createType11 = $models.ApiResponse.createFrom($$createType10);
const $$createType12 = $models.CaptchaData.createFrom;
const $$createType13 = $models.ApiResponse.createFrom($$createType12);
