<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一播放器控制器测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .player-demo {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }
        
        .player-panel {
            flex: 1;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }
        
        .player-panel h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .control-group {
            margin: 15px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .volume-slider {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }
        
        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #4CAF50;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.secondary {
            background: #2196F3;
        }
        
        .btn.secondary:hover {
            background: #1976D2;
        }
        
        .status-display {
            margin: 15px 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .event-log {
            height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-volume { background: rgba(76, 175, 80, 0.2); }
        .log-play { background: rgba(33, 150, 243, 0.2); }
        .log-song { background: rgba(255, 193, 7, 0.2); }
        .log-mute { background: rgba(255, 87, 34, 0.2); }
        
        .controls-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 统一播放器控制器测试</h1>
        
        <div class="test-section">
            <h2>控制器状态</h2>
            <div class="status-display" id="statusDisplay">
                正在加载统一播放器控制器...
            </div>
        </div>
        
        <div class="test-section">
            <h2>双播放器同步测试</h2>
            <div class="player-demo">
                <div class="player-panel">
                    <h3>🎛️ 底栏播放器模拟</h3>
                    <div class="control-group">
                        <label>音量控制</label>
                        <div class="volume-control">
                            <i class="fas fa-volume-up" id="bottomVolumeIcon"></i>
                            <input type="range" class="volume-slider" id="bottomVolumeSlider" min="0" max="100" value="50">
                            <span id="bottomVolumeText">50%</span>
                        </div>
                    </div>
                    <div class="control-group">
                        <button class="btn" onclick="bottomPlayerToggleMute()">切换静音</button>
                        <button class="btn secondary" onclick="bottomPlayerSetVolume(25)">25%</button>
                        <button class="btn secondary" onclick="bottomPlayerSetVolume(75)">75%</button>
                    </div>
                </div>
                
                <div class="player-panel">
                    <h3>🎨 沉浸式播放器模拟</h3>
                    <div class="control-group">
                        <label>音量控制</label>
                        <div class="volume-control">
                            <i class="fas fa-volume-up" id="immersiveVolumeIcon"></i>
                            <input type="range" class="volume-slider" id="immersiveVolumeSlider" min="0" max="100" value="50">
                            <span id="immersiveVolumeText">50%</span>
                        </div>
                    </div>
                    <div class="control-group">
                        <button class="btn" onclick="immersivePlayerToggleMute()">切换静音</button>
                        <button class="btn secondary" onclick="immersivePlayerSetVolume(25)">25%</button>
                        <button class="btn secondary" onclick="immersivePlayerSetVolume(75)">75%</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>统一控制测试</h2>
            <div class="controls-section">
                <button class="btn" onclick="unifiedSetVolume(0)">静音</button>
                <button class="btn" onclick="unifiedSetVolume(25)">25%</button>
                <button class="btn" onclick="unifiedSetVolume(50)">50%</button>
                <button class="btn" onclick="unifiedSetVolume(75)">75%</button>
                <button class="btn" onclick="unifiedSetVolume(100)">100%</button>
                <button class="btn secondary" onclick="unifiedToggleMute()">切换静音</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>事件日志</h2>
            <div class="event-log" id="eventLog"></div>
            <button class="btn secondary" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script src="unified-player-controller.js"></script>
    <script>
        let eventLogElement;
        let statusDisplayElement;
        
        // 模拟播放器元素
        let bottomVolumeSlider, bottomVolumeIcon, bottomVolumeText;
        let immersiveVolumeSlider, immersiveVolumeIcon, immersiveVolumeText;
        
        document.addEventListener('DOMContentLoaded', () => {
            // 获取元素
            eventLogElement = document.getElementById('eventLog');
            statusDisplayElement = document.getElementById('statusDisplay');
            
            bottomVolumeSlider = document.getElementById('bottomVolumeSlider');
            bottomVolumeIcon = document.getElementById('bottomVolumeIcon');
            bottomVolumeText = document.getElementById('bottomVolumeText');
            
            immersiveVolumeSlider = document.getElementById('immersiveVolumeSlider');
            immersiveVolumeIcon = document.getElementById('immersiveVolumeIcon');
            immersiveVolumeText = document.getElementById('immersiveVolumeText');
            
            // 设置事件监听
            setupEventListeners();
            
            // 更新状态显示
            updateStatusDisplay();
            
            // 定期更新状态
            setInterval(updateStatusDisplay, 1000);
        });
        
        function setupEventListeners() {
            if (!window.UnifiedPlayerController) {
                logEvent('error', '统一播放器控制器未加载');
                return;
            }
            
            // 监听统一控制器事件
            window.UnifiedPlayerController.on('volumeChanged', (data) => {
                logEvent('volume', `音量变化: ${data.oldVolume}% → ${data.volume}%`);
                updateAllVolumeDisplays(data.volume);
            });
            
            window.UnifiedPlayerController.on('muteStateChanged', (isMuted) => {
                logEvent('mute', `静音状态: ${isMuted ? '开启' : '关闭'}`);
                updateAllMuteDisplays(isMuted);
            });
            
            // 底栏播放器滑块事件
            bottomVolumeSlider.addEventListener('input', (e) => {
                const volume = parseInt(e.target.value);
                window.UnifiedPlayerController.setVolume(volume);
            });
            
            // 沉浸式播放器滑块事件
            immersiveVolumeSlider.addEventListener('input', (e) => {
                const volume = parseInt(e.target.value);
                window.UnifiedPlayerController.setVolume(volume);
            });
            
            logEvent('info', '事件监听器设置完成');
        }
        
        function updateStatusDisplay() {
            if (!window.UnifiedPlayerController) {
                statusDisplayElement.textContent = '统一播放器控制器未加载';
                return;
            }
            
            const state = window.UnifiedPlayerController.getState();
            statusDisplayElement.innerHTML = `
                <strong>音量:</strong> ${state.volume}% | 
                <strong>静音:</strong> ${state.isMuted ? '是' : '否'} | 
                <strong>播放:</strong> ${state.isPlaying ? '是' : '否'} | 
                <strong>当前时间:</strong> ${window.UnifiedPlayerController.formatTime(state.currentTime)} | 
                <strong>总时长:</strong> ${window.UnifiedPlayerController.formatTime(state.duration)}
            `;
        }
        
        function updateAllVolumeDisplays(volume) {
            // 更新底栏播放器
            bottomVolumeSlider.value = volume;
            bottomVolumeText.textContent = volume + '%';
            updateVolumeIcon(bottomVolumeIcon, volume);
            
            // 更新沉浸式播放器
            immersiveVolumeSlider.value = volume;
            immersiveVolumeText.textContent = volume + '%';
            updateVolumeIcon(immersiveVolumeIcon, volume);
        }
        
        function updateAllMuteDisplays(isMuted) {
            const volume = isMuted ? 0 : window.UnifiedPlayerController.getVolume();
            updateVolumeIcon(bottomVolumeIcon, volume);
            updateVolumeIcon(immersiveVolumeIcon, volume);
        }
        
        function updateVolumeIcon(iconElement, volume) {
            if (volume === 0) {
                iconElement.className = 'fas fa-volume-mute';
            } else if (volume < 30) {
                iconElement.className = 'fas fa-volume-down';
            } else {
                iconElement.className = 'fas fa-volume-up';
            }
        }
        
        function logEvent(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            eventLogElement.appendChild(logEntry);
            eventLogElement.scrollTop = eventLogElement.scrollHeight;
        }
        
        // 控制函数
        function unifiedSetVolume(volume) {
            if (window.UnifiedPlayerController) {
                window.UnifiedPlayerController.setVolume(volume);
            }
        }
        
        function unifiedToggleMute() {
            if (window.UnifiedPlayerController) {
                window.UnifiedPlayerController.toggleMute();
            }
        }
        
        function bottomPlayerToggleMute() {
            unifiedToggleMute();
        }
        
        function bottomPlayerSetVolume(volume) {
            unifiedSetVolume(volume);
        }
        
        function immersivePlayerToggleMute() {
            unifiedToggleMute();
        }
        
        function immersivePlayerSetVolume(volume) {
            unifiedSetVolume(volume);
        }
        
        function clearLog() {
            eventLogElement.innerHTML = '';
        }
    </script>
</body>
</html>
