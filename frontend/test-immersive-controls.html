<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>沉浸式播放器控制测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .immersive-demo {
            position: relative;
            height: 600px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 10px;
            overflow: hidden;
            cursor: none; /* 默认隐藏鼠标指针 */
        }

        /* 鼠标活动时显示指针 */
        .demo-player.show-cursor {
            cursor: default;
        }

        /* 控制元素上始终显示指针 */
        .demo-player button,
        .demo-player input[type="range"],
        .demo-player .volume-icon {
            cursor: pointer !important;
        }
        
        /* 复制沉浸式播放器的控制样式 */
        .immersive-volume-control {
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            padding: 10px 8px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .demo-player.show-controls .immersive-volume-control {
            opacity: 1;
            left: 20px;
        }

        .immersive-volume-control .volume-icon {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            margin-top: 12px;
            cursor: pointer;
            user-select: none;
            transition: color 0.2s ease;
        }

        .immersive-volume-control:hover .volume-icon {
            color: #fff;
        }

        .volume-slider-container {
            position: relative;
            height: 100px;
            width: 4px;
        }

        .volume-slider.vertical {
            position: absolute;
            width: 100px;
            height: 4px;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%) rotate(-90deg);
            transform-origin: center;
            background: transparent;
            outline: none;
            cursor: pointer;
            -webkit-appearance: none;
            appearance: none;
        }

        .volume-slider.vertical::-webkit-slider-track {
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            border: none;
        }

        .volume-slider.vertical::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
            transition: transform 0.2s ease;
        }

        .volume-slider.vertical::-webkit-slider-thumb:hover {
            transform: scale(1.1);
        }

        .immersive-top-controls {
            position: absolute;
            top: 30px;
            right: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
            z-index: 10;
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            transition: all 0.4s ease;
        }

        .demo-player.show-controls .immersive-top-controls {
            opacity: 1;
            visibility: visible;
            pointer-events: all;
        }

        .control-btn {
            width: 40px;
            height: 40px;
            background: transparent;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.4s ease;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .immersive-bottom-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
            padding: 30px;
            pointer-events: all;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .demo-player.show-controls .immersive-bottom-bar {
            opacity: 1;
            transform: translateY(0);
        }

        .demo-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .demo-controls .control-btn {
            width: 50px;
            height: 50px;
            font-size: 18px;
        }

        .status-info {
            margin: 20px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            font-family: monospace;
        }

        .instructions {
            background: rgba(33, 150, 243, 0.2);
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 沉浸式播放器控制测试</h1>
        
        <div class="test-section">
            <h2>鼠标活动检测演示</h2>
            <div class="instructions">
                <h3>📋 测试说明：</h3>
                <ul>
                    <li>🖱️ <strong>鼠标进入</strong>：显示指针和所有控制元素</li>
                    <li>🏃 <strong>鼠标移动</strong>：重置隐藏计时器</li>
                    <li>⏰ <strong>鼠标停止2秒</strong>：鼠标指针自动隐藏</li>
                    <li>⏰ <strong>鼠标停止3秒</strong>：所有控制元素自动隐藏</li>
                    <li>🎯 <strong>悬停控制按钮</strong>：鼠标指针保持显示</li>
                    <li>🚪 <strong>鼠标离开</strong>：立即隐藏指针和控制元素</li>
                </ul>
            </div>
            
            <div class="immersive-demo demo-player" id="demoPlayer">
                <!-- 左侧音量控制 -->
                <div class="immersive-volume-control">
                    <div class="volume-slider-container">
                        <input type="range" class="volume-slider vertical" min="0" max="100" value="50" orient="vertical">
                    </div>
                    <div class="volume-icon">
                        <i class="fas fa-volume-up"></i>
                    </div>
                </div>
                
                <!-- 右上角控制按钮 -->
                <div class="immersive-top-controls">
                    <button class="control-btn" title="全屏">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="control-btn" title="退出">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 底部控制栏 -->
                <div class="immersive-bottom-bar">
                    <div class="demo-controls">
                        <button class="control-btn" title="收藏">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="control-btn" title="上一首">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button class="control-btn" title="播放/暂停">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn" title="下一首">
                            <i class="fas fa-step-forward"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="status-info" id="statusInfo">
                状态：等待鼠标活动...
            </div>
        </div>
    </div>

    <script>
        const demoPlayer = document.getElementById('demoPlayer');
        const statusInfo = document.getElementById('statusInfo');

        let mouseTimer = null;
        let cursorTimer = null;
        let isMouseInside = false;
        
        function updateStatus(message) {
            const timestamp = new Date().toLocaleTimeString();
            statusInfo.textContent = `[${timestamp}] ${message}`;
        }
        
        function showControls() {
            demoPlayer.classList.add('show-controls');
            updateStatus('显示所有控制元素');
        }

        function hideControls() {
            demoPlayer.classList.remove('show-controls');
            updateStatus('隐藏所有控制元素');
        }

        function showCursor() {
            demoPlayer.classList.add('show-cursor');
        }

        function hideCursor() {
            demoPlayer.classList.remove('show-cursor');
            updateStatus('鼠标指针已隐藏');
        }
        
        function resetTimer() {
            clearTimeout(mouseTimer);
            clearTimeout(cursorTimer);
            showControls();
            showCursor();

            if (isMouseInside) {
                // 控制元素3秒后隐藏
                mouseTimer = setTimeout(() => {
                    if (isMouseInside) {
                        hideControls();
                        updateStatus('3秒无活动，自动隐藏控制元素');
                    }
                }, 3000);

                // 鼠标指针2秒后隐藏
                cursorTimer = setTimeout(() => {
                    if (isMouseInside) {
                        hideCursor();
                    }
                }, 2000);

                updateStatus('重置隐藏计时器 (指针2秒，控制3秒)');
            }
        }
        
        // 鼠标进入
        demoPlayer.addEventListener('mouseenter', () => {
            isMouseInside = true;
            resetTimer();
            updateStatus('鼠标进入 - 显示指针和控制元素');
        });

        // 鼠标移动
        demoPlayer.addEventListener('mousemove', () => {
            if (isMouseInside) {
                resetTimer();
            }
        });

        // 鼠标离开
        demoPlayer.addEventListener('mouseleave', () => {
            isMouseInside = false;
            clearTimeout(mouseTimer);
            clearTimeout(cursorTimer);
            hideControls();
            hideCursor();
            updateStatus('鼠标离开 - 立即隐藏指针和控制元素');
        });

        // 为控制按钮添加鼠标事件
        const allButtons = demoPlayer.querySelectorAll('button, input[type="range"], .volume-icon');
        allButtons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                clearTimeout(cursorTimer);
                showCursor();
                updateStatus('悬停控制按钮 - 保持指针显示');
            });
        });
        
        // 初始状态
        updateStatus('准备就绪 - 将鼠标移入上方区域测试');
    </script>
</body>
</html>
