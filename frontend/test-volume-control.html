<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>沉浸式播放器音量控制测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .volume-demo {
            position: relative;
            height: 400px;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            border-radius: 10px;
            overflow: hidden;
        }
        
        /* 复制沉浸式播放器的音量控制样式 */
        .immersive-volume-control {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 20px 15px;
            opacity: 0.7;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .immersive-volume-control:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.5);
            transform: translateY(-50%) scale(1.05);
        }

        .immersive-volume-control .volume-icon {
            color: rgba(255, 255, 255, 0.8);
            font-size: 18px;
            margin-bottom: 15px;
            transition: color 0.3s ease;
            cursor: pointer;
            user-select: none;
        }

        .immersive-volume-control:hover .volume-icon {
            color: #fff;
        }

        .volume-slider-container {
            position: relative;
            height: 150px;
            width: 6px;
            margin: 10px 0;
        }

        .volume-slider.vertical {
            position: absolute;
            width: 150px;
            height: 6px;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%) rotate(-90deg);
            transform-origin: center;
            background: transparent;
            outline: none;
            cursor: pointer;
            -webkit-appearance: none;
            appearance: none;
        }

        .volume-slider.vertical::-webkit-slider-track {
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            border: none;
        }

        .volume-slider.vertical::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
        }

        .volume-slider.vertical::-webkit-slider-thumb:hover {
            transform: scale(1.2);
            background: #f0f0f0;
        }

        .volume-track {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            pointer-events: none;
        }

        .volume-fill {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50%;
            background: linear-gradient(to top, #4CAF50, #81C784);
            border-radius: 3px;
            transition: height 0.2s ease;
        }

        .volume-percentage {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            margin-top: 15px;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .immersive-volume-control:hover .volume-percentage {
            color: #fff;
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 沉浸式播放器音量控制测试</h1>
        
        <div class="test-section">
            <h2>音量控制演示</h2>
            <div class="volume-demo">
                <div class="volume-trigger-area" style="background: rgba(255, 0, 0, 0.1);"></div>
                <div class="immersive-volume-control">
                    <div class="volume-slider-container">
                        <input type="range" class="volume-slider vertical" min="0" max="100" value="50" orient="vertical">
                        <div class="volume-track">
                            <div class="volume-fill"></div>
                        </div>
                    </div>
                    <div class="volume-icon">
                        <i class="fas fa-volume-up"></i>
                    </div>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="setVolume(0)">静音</button>
                <button class="btn" onclick="setVolume(25)">25%</button>
                <button class="btn" onclick="setVolume(50)">50%</button>
                <button class="btn" onclick="setVolume(75)">75%</button>
                <button class="btn" onclick="setVolume(100)">100%</button>
                <button class="btn" onclick="toggleMute()">切换静音</button>
            </div>
            
            <div class="info">
                <h3>功能说明：</h3>
                <ul>
                    <li>🎚️ 拖拽滑块调节音量</li>
                    <li>🖱️ 鼠标滚轮调节音量</li>
                    <li>🔇 点击音量图标切换静音</li>
                    <li>📱 悬停效果和动画</li>
                    <li>🎯 垂直布局，适合左侧边缘</li>
                </ul>
                <p><strong>当前音量：</strong><span id="currentVolume">50%</span></p>
            </div>
        </div>
    </div>

    <script>
        let lastVolume = 50;
        
        const volumeSlider = document.querySelector('.volume-slider');
        const volumeIcon = document.querySelector('.volume-icon i');
        const volumeFill = document.querySelector('.volume-fill');
        const volumePercentage = document.querySelector('.volume-percentage');
        const currentVolumeSpan = document.getElementById('currentVolume');
        const volumeControl = document.querySelector('.immersive-volume-control');
        
        // 音量滑块事件
        volumeSlider.addEventListener('input', (e) => {
            const volume = parseInt(e.target.value);
            updateVolumeDisplay(volume);
        });
        
        // 音量图标点击事件
        volumeIcon.addEventListener('click', () => {
            toggleMute();
        });
        
        // 鼠标滚轮控制音量
        volumeControl.addEventListener('wheel', (e) => {
            e.preventDefault();
            const currentVolume = parseInt(volumeSlider.value);
            const delta = e.deltaY > 0 ? -5 : 5;
            const newVolume = Math.max(0, Math.min(100, currentVolume + delta));
            setVolume(newVolume);
        });
        
        function setVolume(volume) {
            volume = Math.max(0, Math.min(100, volume));
            volumeSlider.value = volume;
            updateVolumeDisplay(volume);
        }
        
        function updateVolumeDisplay(volume) {
            volumeFill.style.height = `${volume}%`;
            volumePercentage.textContent = `${volume}%`;
            currentVolumeSpan.textContent = `${volume}%`;
            
            // 更新音量图标
            if (volume === 0) {
                volumeIcon.className = 'fas fa-volume-mute';
            } else if (volume < 30) {
                volumeIcon.className = 'fas fa-volume-down';
            } else if (volume < 70) {
                volumeIcon.className = 'fas fa-volume-up';
            } else {
                volumeIcon.className = 'fas fa-volume-up';
            }
        }
        
        function toggleMute() {
            const currentVolume = parseInt(volumeSlider.value);
            
            if (currentVolume === 0) {
                setVolume(lastVolume || 50);
            } else {
                lastVolume = currentVolume;
                setVolume(0);
            }
        }
        
        // 初始化显示
        updateVolumeDisplay(50);
    </script>
</body>
</html>
