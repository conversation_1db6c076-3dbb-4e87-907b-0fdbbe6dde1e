<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>底栏音量图标测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        /* 模拟底栏播放器样式 */
        .player-bar {
            background: #2a2a3e;
            padding: 15px 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
        }
        
        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .volume-btn {
            background: transparent;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }
        
        .volume-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .volume-slider {
            width: 100px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }
        
        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #4CAF50;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 20px 0;
        }
        
        .icon-demo {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }
        
        .icon-demo i {
            font-size: 24px;
            width: 30px;
            text-align: center;
        }
        
        .mute { color: #f44336; }
        .low { color: #ff9800; }
        .high { color: #4caf50; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔊 底栏音量图标测试</h1>
        
        <div class="test-section">
            <h2>模拟底栏播放器</h2>
            <div class="player-bar">
                <div>模拟播放器</div>
                <div class="volume-control">
                    <button class="volume-btn" title="音量">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <input type="range" class="volume-slider" min="0" max="100" value="50" title="音量控制">
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>音量图标状态演示</h2>
            <div class="icon-demo">
                <i class="fas fa-volume-mute mute"></i>
                <span>静音 (0%)</span>
            </div>
            <div class="icon-demo">
                <i class="fas fa-volume-down low"></i>
                <span>低音量 (1-29%)</span>
            </div>
            <div class="icon-demo">
                <i class="fas fa-volume-up high"></i>
                <span>正常音量 (30-100%)</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>音量控制测试</h2>
            <div class="controls">
                <button class="btn" onclick="setVolume(0)">静音 (0%)</button>
                <button class="btn" onclick="setVolume(15)">低音量 (15%)</button>
                <button class="btn" onclick="setVolume(30)">中音量 (30%)</button>
                <button class="btn" onclick="setVolume(60)">高音量 (60%)</button>
                <button class="btn" onclick="setVolume(100)">最大音量 (100%)</button>
                <button class="btn" onclick="toggleMute()">切换静音</button>
            </div>
            
            <div class="status" id="status">
                当前音量：50% | 图标状态：正常
            </div>
        </div>
    </div>

    <script>
        const volumeSlider = document.querySelector('.volume-slider');
        const volumeIcon = document.querySelector('.volume-btn i');
        const statusElement = document.getElementById('status');
        
        let lastVolume = 50;
        let isMuted = false;
        
        // 更新音量图标
        function updateVolumeIcon(volume) {
            if (volume === 0) {
                volumeIcon.className = 'fas fa-volume-mute';
                return '静音';
            } else if (volume < 30) {
                volumeIcon.className = 'fas fa-volume-down';
                return '低音量';
            } else {
                volumeIcon.className = 'fas fa-volume-up';
                return '正常音量';
            }
        }
        
        // 更新状态显示
        function updateStatus(volume) {
            const iconStatus = updateVolumeIcon(volume);
            statusElement.textContent = `当前音量：${volume}% | 图标状态：${iconStatus}`;
            console.log('🔊 音量更新:', volume + '%', '图标:', volumeIcon.className);
        }
        
        // 设置音量
        function setVolume(volume) {
            volumeSlider.value = volume;
            isMuted = volume === 0;
            if (volume > 0) {
                lastVolume = volume;
            }
            updateStatus(volume);
        }
        
        // 切换静音
        function toggleMute() {
            if (isMuted) {
                setVolume(lastVolume);
            } else {
                lastVolume = parseInt(volumeSlider.value);
                setVolume(0);
            }
        }
        
        // 滑块事件
        volumeSlider.addEventListener('input', (e) => {
            const volume = parseInt(e.target.value);
            isMuted = volume === 0;
            if (volume > 0) {
                lastVolume = volume;
            }
            updateStatus(volume);
        });
        
        // 音量按钮点击事件
        volumeIcon.addEventListener('click', toggleMute);
        
        // 初始化
        updateStatus(50);
        
        console.log('🔊 底栏音量图标测试页面已加载');
    </script>
</body>
</html>
