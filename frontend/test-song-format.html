<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>歌曲信息格式化测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .test-input { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .test-output { background: #e8f5e8; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .error { background: #ffe8e8; }
    </style>
</head>
<body>
    <h1>🎵 歌曲信息格式化函数测试</h1>
    
    <div id="test-results"></div>

    <script src="html5-audio-player-unified.js"></script>
    <script>
        // 测试用例
        const testCases = [
            {
                name: "标准歌曲对象",
                input: {
                    hash: "abc123",
                    songname: "标准歌名",
                    author_name: "标准艺术家",
                    album_name: "标准专辑",
                    union_cover: "cover.jpg",
                    time_length: 240
                }
            },
            {
                name: "使用title字段的歌曲",
                input: {
                    hash: "def456",
                    title: "使用title的歌名",
                    author_name: "艺术家",
                    album_name: "专辑",
                    time_length: 180
                }
            },
            {
                name: "使用name字段的歌曲",
                input: {
                    hash: "ghi789",
                    name: "使用name的歌名",
                    author_name: "艺术家",
                    time_length: 200
                }
            },
            {
                name: "使用filename字段的歌曲",
                input: {
                    hash: "jkl012",
                    filename: "使用filename的歌名.mp3",
                    author_name: "艺术家"
                }
            },
            {
                name: "缺少歌名字段的歌曲",
                input: {
                    hash: "mno345",
                    author_name: "艺术家",
                    album_name: "专辑"
                }
            },
            {
                name: "缺少艺术家字段的歌曲",
                input: {
                    hash: "pqr678",
                    songname: "歌名",
                    album_name: "专辑"
                }
            },
            {
                name: "空对象",
                input: {}
            },
            {
                name: "null对象",
                input: null
            },
            {
                name: "undefined对象",
                input: undefined
            }
        ];

        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            
            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                
                try {
                    const result = window.formatSongInfo(testCase.input);
                    
                    testDiv.innerHTML = `
                        <div class="test-title">测试 ${index + 1}: ${testCase.name}</div>
                        <div class="test-input">
                            <strong>输入:</strong><br>
                            <pre>${JSON.stringify(testCase.input, null, 2)}</pre>
                        </div>
                        <div class="test-output">
                            <strong>输出:</strong><br>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    testDiv.innerHTML = `
                        <div class="test-title">测试 ${index + 1}: ${testCase.name}</div>
                        <div class="test-input">
                            <strong>输入:</strong><br>
                            <pre>${JSON.stringify(testCase.input, null, 2)}</pre>
                        </div>
                        <div class="test-output error">
                            <strong>错误:</strong><br>
                            <pre>${error.message}</pre>
                        </div>
                    `;
                }
                
                resultsContainer.appendChild(testDiv);
            });
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', () => {
            // 等待一下确保formatSongInfo函数已加载
            setTimeout(runTests, 100);
        });
    </script>
</body>
</html>
