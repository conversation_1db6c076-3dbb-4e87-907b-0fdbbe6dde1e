# 🎤 GoMusic Plasma 歌词卡拉OK功能详解

## 概述

GoMusic Plasma 歌词插件现在支持完整的卡拉OK效果！当播放KRC格式歌词时，插件会显示逐字高亮动画，让你享受真正的卡拉OK体验。

## 🎯 核心特性

### 🎨 视觉效果
- **逐字高亮**: 每个字符按时间精确高亮
- **颜色动画**: 平滑的颜色过渡效果 (200ms)
- **缩放效果**: 高亮字符放大1.1倍
- **主题集成**: 使用Plasma主题的强调色和文本色

### ⏱️ 时间同步
- **精确解析**: 解析KRC格式的毫秒级时间信息
- **实时更新**: 50ms间隔的高频更新
- **自动完成**: 检测动画完成并自动停止

### 🔄 智能处理
- **格式检测**: 自动识别KRC和LRC格式
- **错误恢复**: 解析失败时回退到纯文本显示
- **性能优化**: 高效的数组更新机制

## 📋 技术实现

### KRC格式解析
```javascript
// KRC格式示例
[171960,5040]<0,240,0>你<240,150,0>走<390,240,0>之<630,240,0>后

// 解析结果
{
  lineStartTime: 171960,  // 行开始时间(毫秒)
  lineDuration: 5040,     // 行持续时间(毫秒)
  chars: ["你", "走", "之", "后"],
  timings: [
    { startTime: 0, duration: 240 },    // 你: 0-240ms
    { startTime: 240, duration: 150 },  // 走: 240-390ms
    { startTime: 390, duration: 240 },  // 之: 390-630ms
    { startTime: 630, duration: 240 }   // 后: 630-870ms
  ]
}
```

### 动画系统
```qml
// 高亮状态更新 (50ms间隔)
Timer {
    interval: 50
    repeat: true
    running: karaokeActive
    onTriggered: updateKaraokeHighlight()
}

// 颜色动画
Behavior on color {
    ColorAnimation {
        duration: 200
        easing.type: Easing.OutQuad
    }
}

// 缩放动画
transform: Scale {
    xScale: highlighted ? 1.1 : 1.0
    yScale: highlighted ? 1.1 : 1.0
    
    Behavior on xScale {
        NumberAnimation {
            duration: 200
            easing.type: Easing.OutBack
        }
    }
}
```

## 🚀 使用方法

### 1. 安装插件
```bash
cd lyric/plasma-lyrics
./install.sh
```

### 2. 添加到桌面
1. 右键点击桌面或面板
2. 选择"添加部件"
3. 搜索"GoMusic Lyrics"
4. 拖拽到桌面或面板

### 3. 播放KRC歌曲
- 确保GoMusic播放器运行在 `http://127.0.0.1:18911`
- 播放支持KRC格式的歌曲
- 享受卡拉OK效果！

## 🧪 测试验证

### 运行测试
```bash
# KRC解析逻辑测试
node test-krc.js

# 功能演示
./demo-karaoke.sh

# 连接测试
./test.sh
```

### 测试结果
```
🧪 开始KRC格式解析测试
========================

测试 1: 简单测试 ✅
测试 2: 复杂测试 ✅  
测试 3: 英文测试 ✅

📊 测试结果: 3/3 通过 (100.0%)
🎉 所有测试通过！KRC解析功能正常
```

## 🎵 支持的格式

### KRC格式 (卡拉OK)
```
[开始时间,持续时间]<字符开始,字符持续,保留>字符<...>字符...
```
- **特点**: 每个字符都有精确时间信息
- **效果**: 逐字高亮动画
- **示例**: `[171960,5040]<0,240,0>你<240,150,0>走`

### LRC格式 (标准)
```
[分:秒.毫秒]歌词文本
```
- **特点**: 整行时间标记
- **效果**: 纯文本显示
- **示例**: `[02:51.96]你走之后我又 再为谁等候`

## 🔧 配置选项

### 动画参数
- **更新频率**: 50ms (可在代码中调整)
- **颜色过渡**: 200ms OutQuad缓动
- **缩放动画**: 200ms OutBack缓动
- **缩放比例**: 1.1倍 (可自定义)

### 主题适配
- **高亮颜色**: `theme.highlightColor`
- **普通颜色**: `theme.textColor`
- **字体大小**: `theme.defaultFont.pointSize + 2`
- **字体粗细**: `font.bold: true`

## 🐛 故障排除

### 卡拉OK效果不显示
1. **检查歌词格式**: 确认是KRC格式而非LRC
2. **查看控制台**: 检查解析错误信息
3. **验证时间标记**: 确认KRC包含正确的时间信息

### 动画不流畅
1. **系统性能**: 确保系统资源充足
2. **Plasma版本**: 使用Plasma 6.0+获得最佳效果
3. **Qt版本**: 确保Qt版本支持现代动画API

### 连接问题
1. **服务状态**: 确认GoMusic服务正在运行
2. **端口检查**: 验证18911端口可访问
3. **网络设置**: 检查防火墙和代理设置

## 🎉 享受卡拉OK！

现在你可以享受完整的卡拉OK体验了！插件会自动识别歌词格式，为KRC歌词提供精美的逐字高亮动画，让你的音乐体验更加生动有趣。

**特别提示**: 卡拉OK效果需要歌曲本身支持KRC格式。如果歌曲只有LRC格式歌词，插件会自动回退到普通文本显示模式。
