# GoMusic Plasma 歌词插件

这是一个为 KDE Plasma 桌面环境设计的歌词显示插件，通过 SSE (Server-Sent Events) 连接从 GoMusic 播放器获取实时歌词。

## 功能特性

- 🎵 实时歌词显示
- 🎤 **卡拉OK效果** - KRC格式逐字高亮动画
- 🔄 自动重连机制
- 🚦 **三色状态指示** - 橙色(连接中)/绿色(已连接)/红色(未连接)
- 🚫 **纯净界面** - SSE断开时完全隐藏歌词，不显示等待提示
- 🔍 **连接检测优化** - 检测时只显示状态指示器，隐藏歌词区域
- 🎨 与 Plasma 主题集成
- 💫 **智能布局** - 防止歌词覆盖状态指示器
- ✨ 平滑动画效果
- 🎯 精确时间同步
- 📏 **动态宽度** - 根据歌词长度自适应组件宽度
- 📜 **智能滚动** - 长歌词自动滚动，短歌词紧凑显示
- 🎯 **自动跟随** - 卡拉OK时自动滚动到当前字符
- 📐 **右对齐布局** - 歌词文本右对齐显示，与状态指示器协调
- ⚡ **性能优化** - 低CPU占用，智能定时器控制

## 安装方法

### 方法一：手动安装

1. 将整个 `plasma-lyrics` 文件夹复制到 Plasma 插件目录：
   ```bash
   cp -r plasma-lyrics ~/.local/share/plasma/plasmoids/
   ```

2. 重启 Plasma Shell：
   ```bash
   killall plasmashell && plasmashell &
   ```

### 方法二：使用 Plasma 包管理器

1. 打开系统设置 → 桌面行为 → 桌面效果
2. 点击"获取新插件"
3. 选择"从文件安装"
4. 选择 plasma-lyrics 文件夹

## 使用方法

1. 右键点击桌面或面板
2. 选择"添加部件"
3. 搜索"GoMusic Lyrics"
4. 将插件拖拽到桌面或面板上

## 配置要求

- GoMusic 播放器需要运行在 `http://127.0.0.1:18911`
- SSE 歌词服务端点：`http://127.0.0.1:18911/api/osd-lyrics/sse`
- KDE Plasma 6.0 或更高版本

## 功能说明

### 连接状态指示器
- 🟢 绿色：已连接到歌词服务
- 🔴 红色：未连接到歌词服务

### 歌词格式支持
- **LRC 格式**：标准歌词格式，显示纯文本
- **KRC 格式**：卡拉OK格式，**逐字高亮动画效果**
  - 🎨 颜色渐变动画 (200ms平滑过渡)
  - 📏 字符缩放效果 (高亮时放大1.1倍)
  - ⏱️ 精确时间同步 (50ms更新间隔)
  - 🎯 自动完成检测

### 自动重连
- 连接断开时自动重试
- 30秒心跳检测
- 3秒重连间隔

## 故障排除

### 歌词不显示
1. 检查 GoMusic 播放器是否正在运行
2. 确认歌词服务端点是否可访问：
   ```bash
   curl http://127.0.0.1:18911/api/osd-lyrics/sse
   ```
3. 查看插件连接状态指示器

### 卡拉OK效果不显示
1. 确认歌曲使用KRC格式歌词
2. 检查歌词数据是否包含时间标记
3. 查看浏览器控制台日志

### 加载问题
1. "Element is not creatable" → 已修复，使用正确的主题API
2. theme未定义错误 → 已修复，使用Plasmoid.theme
3. 插件无法加载 → 重新安装: `./uninstall.sh && ./install.sh`

### 布局问题
1. 歌词覆盖状态指示器 → 已修复，智能布局
2. 歌词上方被截断 → 已修复，移除上下边距
3. 文本过长溢出 → 水平滚动，动态宽度

### 连接问题
1. 确认防火墙设置
2. 检查端口 18911 是否被占用
3. 重启 GoMusic 播放器

## 🎤 卡拉OK效果演示

运行演示脚本了解卡拉OK功能：
```bash
./demo-karaoke.sh
```

### KRC格式示例
```
[171960,5040]<0,240,0>你<240,150,0>走<390,240,0>之<630,240,0>后<870,240,0>我<1110,240,0>又
```

### 动画效果
- **高亮颜色**: Plasma主题强调色
- **普通颜色**: Plasma主题文本色
- **缩放动画**: 1.0x → 1.1x → 1.0x
- **过渡时间**: 200ms平滑动画
- **更新频率**: 50ms精确同步

### 动态布局
- **组件宽度**: 根据歌词长度自适应 (最小200px)
- **字体大小**: 系统默认大小，提升可读性
- **滚动方式**: 仅水平滚动
- **自动跟随**: 卡拉OK时滚动到当前字符
- **响应式**: 短歌词紧凑，长歌词扩展

## 开发信息

- **版本**：2.0
- **许可证**：GPL-3.0
- **兼容性**：KDE Plasma 6.0+
- **语言**：QML/JavaScript

## 更新日志

### v2.9 (最新)
- ⚡ **性能优化** - 大幅降低CPU占用，优化定时器频率和算法效率
- 📐 **右对齐布局** - 歌词文本采用右对齐显示，与状态指示器形成协调布局
- 🚫 **纯净界面** - SSE断开时完全隐藏歌词文本，不显示任何等待提示
- 🔍 **连接检测优化** - 检测SSE服务器时只显示状态指示器，隐藏歌词区域
- 🚦 **三色状态指示** - 橙色(连接中)、绿色(已连接)、红色(未连接)

### v2.3
- 🔧 **修复加载错误** - 解决"Element is not creatable"问题
- 🎨 **主题系统重构** - 使用正确的Plasmoid.theme API
- 📜 **水平滚动** - 20个汉字固定宽度，超长自动滚动
- 🎯 **自动跟随** - 卡拉OK时自动滚动到当前字符

### v2.1
- 🎤 **新增卡拉OK效果** - KRC格式逐字高亮动画
- 🎨 **布局优化** - 防止歌词覆盖状态指示器
- 📐 **响应式设计** - 动态宽度计算 (200px-500px)
- ✂️ **智能截断** - 自动处理过长文本
- ✨ **动画增强** - 颜色渐变和缩放效果
- 🔧 **性能优化** - 50ms高频更新，流畅同步

### v2.0
- 重构为使用 SSE 连接
- 添加连接状态指示器
- 改进错误处理和重连机制
- 支持 KRC 和 LRC 格式歌词
- 更新 UI 设计

### v1.0
- 初始版本
- 基于 HTTP 轮询的歌词获取
