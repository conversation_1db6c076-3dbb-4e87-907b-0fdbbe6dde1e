version: '3'

tasks:
  go:mod:tidy:
    summary: Runs `go mod tidy`
    internal: true
    cmds:
      - go mod tidy

  install:frontend:deps:
    summary: Install frontend dependencies
    dir: frontend
    sources:
      - package.json
      - package-lock.json
    generates:
      - node_modules/*
    preconditions:
      - sh: npm version
        msg: "Looks like npm isn't installed. Npm is part of the Node installer: https://nodejs.org/en/download/"
    cmds:
      - npm install

  build:frontend:
    summary: Build the frontend project
    dir: frontend
    sources:
      - "**/*"
    generates:
      - dist/*
    deps:
      - task: install:frontend:deps
      - task: generate:bindings
    cmds:
      - npm run {{.BUILD_COMMAND}} -q
    env:
      PRODUCTION: '{{.PRODUCTION | default "false"}}'
    vars:
      BUILD_COMMAND: '{{if eq .PRODUCTION "true"}}build{{else}}build:dev{{end}}'


  generate:bindings:
    summary: Generates bindings for the frontend
    deps:
      - task: go:mod:tidy
    sources:
      - "**/*.go"
      - go.mod
      - go.sum
    generates:
      - "frontend/bindings/**/*"
    cmds:
      - wails3 generate bindings -f '{{.BUILD_FLAGS}}' -clean=true 

  generate:icons:
    summary: Generates Windows `.ico` and Mac `.icns` files from an image
    dir: build
    sources:
      - "appicon.png"
    generates:
      - "icons.icns"
      - "icons.ico"
    cmds:
      - wails3 generate icons -input appicon.png -macfilename darwin/icons.icns -windowsfilename windows/icons.ico

  dev:frontend:
    summary: Runs the frontend in development mode
    dir: frontend
    deps:
      - task: install:frontend:deps
    cmds:
      - npm run dev -- --port {{.VITE_PORT}} --strictPort

  update:build-assets:
    summary: Updates the build assets
    dir: build
    cmds:
      - wails3 update build-assets -name "{{.APP_NAME}}" -binaryname "{{.APP_NAME}}" -config config.yml -dir .