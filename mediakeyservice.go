package main

import (
	"context"
	"log"
)

// MediaKeyService 媒体键服务
// 由于 Wails 3 可能不支持全局快捷键，我们使用前端键盘事件监听
type MediaKeyService struct {
	ctx context.Context
}

// NewMediaKeyService 创建媒体键服务
func NewMediaKeyService() *MediaKeyService {
	return &MediaKeyService{}
}

// SetContext 设置上下文
func (m *MediaKeyService) SetContext(ctx context.Context) {
	m.ctx = ctx
}

// SetApp 设置应用实例（保持兼容性）
func (m *MediaKeyService) SetApp(app interface{}) {
	// 在 Wails 3 中，我们主要依赖前端的键盘事件监听
	log.Println("🎵 媒体键服务：使用前端键盘事件监听模式")
}

// RegisterMediaKeys 注册媒体键（前端实现）
func (m *MediaKeyService) RegisterMediaKeys() error {
	log.Println("🎵 媒体键服务：前端键盘监听已启用")
	log.Println("📋 支持的媒体键:")
	log.Println("   - 播放/暂停: Space 键")
	log.Println("   - 下一首: → 方向键")
	log.Println("   - 上一首: ← 方向键")
	log.Println("   - 音量调节: ↑↓ 方向键")
	return nil
}

// UnregisterMediaKeys 取消注册媒体键
func (m *MediaKeyService) UnregisterMediaKeys() {
	log.Println("🎵 媒体键服务：已停用")
}

// GetMediaKeyStatus 获取媒体键状态
func (m *MediaKeyService) GetMediaKeyStatus() map[string]interface{} {
	return map[string]interface{}{
		"success": true,
		"message": "媒体键服务运行中（前端键盘监听模式）",
		"keys": []string{
			"Space - 播放/暂停",
			"ArrowRight - 下一首",
			"ArrowLeft - 上一首",
			"ArrowUp - 音量+",
			"ArrowDown - 音量-",
		},
	}
}