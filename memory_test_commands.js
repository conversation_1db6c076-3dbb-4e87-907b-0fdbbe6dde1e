// 内存监控测试命令
// 在浏览器控制台中运行这些命令来测试内存监控功能

console.log('🧪 开始内存监控测试...');

// 1. 检查资源管理器是否正常工作
console.log('📊 检查资源管理器状态:');
if (window.GlobalResourceManager) {
    console.log('✅ GlobalResourceManager 已加载');
    console.log('📊 当前资源统计:', window.GlobalResourceManager.getStats());
} else {
    console.log('❌ GlobalResourceManager 未加载');
}

// 2. 检查内存监控器是否正常工作
console.log('📊 检查内存监控器状态:');
if (window.MemoryMonitor) {
    console.log('✅ MemoryMonitor 已加载');
    console.log('📊 当前内存信息:', window.MemoryMonitor.getMemoryInfo());
} else {
    console.log('❌ MemoryMonitor 未加载');
}

// 3. 测试便捷命令
console.log('📊 测试便捷命令:');
try {
    console.log('memoryInfo():', memoryInfo());
    console.log('resourceStats():', resourceStats());
} catch (error) {
    console.log('❌ 便捷命令测试失败:', error);
}

// 4. 创建一些测试资源来验证资源管理器
console.log('🧪 创建测试资源...');

// 创建测试定时器
const testTimer = window.GlobalResourceManager.addTimer(() => {
    console.log('🧪 测试定时器触发');
}, 5000);

// 创建测试间隔定时器
const testInterval = window.GlobalResourceManager.addInterval(() => {
    console.log('🧪 测试间隔定时器触发');
}, 3000);

// 创建测试音频实例
const testAudio = window.GlobalResourceManager.createAudio();

console.log('📊 创建测试资源后的统计:', window.GlobalResourceManager.getStats());

// 5. 清理测试资源
setTimeout(() => {
    console.log('🧹 清理测试资源...');
    window.GlobalResourceManager.removeTimer(testTimer);
    window.GlobalResourceManager.removeInterval(testInterval);
    window.GlobalResourceManager.destroyAudio(testAudio);
    console.log('📊 清理后的资源统计:', window.GlobalResourceManager.getStats());
}, 10000);

// 6. 生成内存报告
setTimeout(() => {
    console.log('📋 生成内存报告:');
    console.log(memoryReport());
}, 15000);

console.log('🧪 内存监控测试命令已设置，请等待结果...');
